# AUTHENTICATION & SECURITY TESTING IMPLEMENTATION GUIDE
# Complete Testing Plan for Auth, Cookies, Sessions & Security

## OVERVIEW
This document provides detailed implementation steps for testing all authentication, cookie handling, session management, and security components in the AranCare application.

## CRITICAL SECURITY VULNERABILITIES TO TEST

### 1. HARDCODED CREDENTIALS REMOVAL
**Location**: `apps/frontend/src/app/api/auth/[...nextauth]/route.ts`

[ ] Test Hardcoded Credential Removal
```javascript
// Test file: tests/security/hardcoded-credentials.test.js
describe('Hardcoded Credentials Security', () => {
  it('<NAME_EMAIL>/demo123', async () => {
    const response = await request(BASE_URL)
      .post('/api/auth/signin')
      .send({ email: '<EMAIL>', password: 'demo123' });
    expect(response.status).to.be.oneOf([401, 404]);
  });

  it('<NAME_EMAIL>/admin123', async () => {
    const response = await request(BASE_URL)
      .post('/api/auth/signin')
      .send({ email: '<EMAIL>', password: 'admin123' });
    expect(response.status).to.be.oneOf([401, 404]);
  });
});
```

[ ] Verify Database Authentication Only
- Test that only valid database users can authenticate
- Verify password hashing with bcrypt
- Test authentication against Prisma database
- Validate user role assignment from database

### 2. API KEY AUTHENTICATION TESTING
**Location**: `apps/node-server/src/middleware/auth.js`

[ ] Test API Key Enforcement
```javascript
// Test file: tests/security/api-key-auth.test.js
describe('API Key Authentication', () => {
  it('should require API key for Fidelius endpoints', async () => {
    const response = await request(NODE_SERVER_URL)
      .post('/fidelius-api/encrypt')
      .send({ string_to_encrypt: 'test' });
    expect(response.status).to.equal(401);
  });

  it('should validate API key correctness', async () => {
    const response = await request(NODE_SERVER_URL)
      .post('/fidelius-api/encrypt')
      .set('x-api-key', 'invalid-key')
      .send({ string_to_encrypt: 'test' });
    expect(response.status).to.equal(401);
  });

  it('should accept valid API key', async () => {
    const response = await request(NODE_SERVER_URL)
      .post('/fidelius-api/encrypt')
      .set('x-api-key', process.env.API_KEY)
      .send({ 
        string_to_encrypt: 'test',
        sender_nonce: 'test-nonce',
        requester_nonce: 'test-nonce',
        sender_private_key: 'test-key',
        requester_public_key: 'test-key'
      });
    expect(response.status).to.not.equal(401);
  });
});
```

### 3. CORS CONFIGURATION TESTING
**Location**: `apps/node-server/src/index.js`

[ ] Test CORS Security
```javascript
// Test file: tests/security/cors.test.js
describe('CORS Configuration', () => {
  it('should not allow wildcard origins', async () => {
    const response = await request(NODE_SERVER_URL)
      .options('/fidelius-api/health')
      .set('Origin', 'https://malicious-site.com');
    expect(response.headers['access-control-allow-origin']).to.not.equal('*');
  });

  it('should allow configured origins only', async () => {
    const response = await request(NODE_SERVER_URL)
      .options('/fidelius-api/health')
      .set('Origin', 'http://localhost:3005');
    expect(response.status).to.equal(200);
  });
});
```

### 4. ENVIRONMENT VARIABLE SECURITY TESTING

[ ] Test Client Secret Protection
```javascript
// Test file: tests/security/environment-vars.test.js
describe('Environment Variable Security', () => {
  it('should not expose ABDM_CLIENT_SECRET in client', async () => {
    const response = await request(BASE_URL).get('/');
    const html = response.text;
    expect(html).to.not.include('ABDM_CLIENT_SECRET');
    expect(html).to.not.include(process.env.ABDM_CLIENT_SECRET);
  });

  it('should use server-only environment variables', () => {
    // Verify ABDM_CLIENT_SECRET is not in NEXT_PUBLIC_ format
    expect(process.env.NEXT_PUBLIC_ABDM_CLIENT_SECRET).to.be.undefined;
    expect(process.env.ABDM_CLIENT_SECRET).to.exist;
  });
});
```

## AUTHENTICATION SYSTEM TESTING

### 5. NEXTAUTH CONFIGURATION TESTING
**Location**: `apps/frontend/src/app/api/auth/[...nextauth]/route.ts`

[ ] Test NextAuth Security Configuration
```javascript
// Test file: tests/auth/nextauth-config.test.js
describe('NextAuth Configuration', () => {
  it('should use JWT strategy', async () => {
    // Test JWT token generation
    const loginResponse = await request(BASE_URL)
      .post('/api/auth/signin')
      .send({ email: '<EMAIL>', password: 'validpass' });
    
    if (loginResponse.status === 200) {
      const cookies = loginResponse.headers['set-cookie'];
      const sessionCookie = cookies.find(c => c.includes('next-auth.session-token'));
      expect(sessionCookie).to.exist;
    }
  });

  it('should redirect to sign-in on error', async () => {
    const response = await request(BASE_URL)
      .get('/api/auth/error');
    expect(response.status).to.be.oneOf([302, 200]);
  });

  it('should have secure session configuration', () => {
    // Verify session strategy is JWT
    // Verify secret is configured
    // Test session callbacks
  });
});
```

### 6. PASSWORD SECURITY TESTING
**Location**: `apps/frontend/src/lib/auth.ts`

[ ] Test Password Hashing and Validation
```javascript
// Test file: tests/auth/password-security.test.js
describe('Password Security', () => {
  it('should hash passwords with bcrypt', async () => {
    const { hashPassword, comparePassword } = require('../src/lib/auth');
    const password = 'testpassword123';
    const hashed = await hashPassword(password);
    
    expect(hashed).to.not.equal(password);
    expect(hashed).to.include('$2b$');
    
    const isValid = await comparePassword(password, hashed);
    expect(isValid).to.be.true;
  });

  it('should reject weak passwords', async () => {
    const response = await request(BASE_URL)
      .post('/api/auth/register')
      .send({
        email: '<EMAIL>',
        password: '123', // Weak password
        name: 'Test User'
      });
    expect(response.status).to.equal(400);
  });
});
```

## COOKIE & SESSION SECURITY TESTING

### 7. COOKIE SECURITY TESTING
**Location**: `apps/frontend/src/lib/auth-cookies.ts`, `src/lib/client-cookies.ts`

[ ] Test Cookie Security Implementation
```javascript
// Test file: tests/auth/cookie-security.test.js
describe('Cookie Security', () => {
  it('should set secure cookie flags', async () => {
    const response = await request(BASE_URL)
      .post('/api/login')
      .send({ email: '<EMAIL>', password: 'validpass' });
    
    if (response.status === 200) {
      const cookies = response.headers['set-cookie'];
      const sessionCookie = cookies.find(c => c.includes('session-token'));
      
      if (sessionCookie) {
        expect(sessionCookie).to.include('HttpOnly');
        expect(sessionCookie).to.include('Path=/');
        if (process.env.NODE_ENV === 'production') {
          expect(sessionCookie).to.include('Secure');
        }
      }
    }
  });

  it('should validate cookie parsing security', () => {
    const { getUserFromCookies } = require('../src/lib/auth-cookies');
    // Test cookie parsing with malicious input
    // Verify XSS prevention in cookie values
    // Test cookie tampering detection
  });

  it('should handle client-side cookie access securely', () => {
    const { getCurrentUserClient } = require('../src/lib/client-session');
    // Test client-side cookie reading
    // Verify no sensitive data exposure
    // Test cookie decoding security
  });
});
```

### 8. SESSION MANAGEMENT TESTING
**Location**: `apps/frontend/src/lib/session.ts`

[ ] Test Session Security
```javascript
// Test file: tests/auth/session-security.test.js
describe('Session Management', () => {
  it('should validate session tokens', async () => {
    const { getCurrentUser } = require('../src/lib/session');
    
    // Test with valid session
    // Test with expired session
    // Test with tampered session
    // Test with missing session
  });

  it('should handle organization status validation', async () => {
    const { isCurrentOrganizationActive } = require('../src/lib/session');
    
    // Test with active organization
    // Test with inactive organization
    // Test with missing organization
    // Test error handling
  });

  it('should prevent session fixation', async () => {
    // Test session regeneration on login
    // Test session invalidation on logout
    // Test concurrent session handling
  });
});
```

## MIDDLEWARE SECURITY TESTING

### 9. SECURITY HEADERS TESTING
**Location**: `apps/frontend/src/middleware/security-headers.ts`

[ ] Test Security Headers Implementation
```javascript
// Test file: tests/security/security-headers.test.js
describe('Security Headers', () => {
  it('should set comprehensive security headers', async () => {
    const response = await request(BASE_URL).get('/dashboard');
    
    expect(response.headers['content-security-policy']).to.exist;
    expect(response.headers['x-frame-options']).to.equal('DENY');
    expect(response.headers['x-content-type-options']).to.equal('nosniff');
    expect(response.headers['referrer-policy']).to.exist;
    expect(response.headers['x-xss-protection']).to.equal('1; mode=block');
  });

  it('should set healthcare-specific headers', async () => {
    const response = await request(BASE_URL).get('/dashboard');
    
    expect(response.headers['x-healthcare-app']).to.equal('AranCare');
    expect(response.headers['cache-control']).to.include('no-store');
    expect(response.headers['pragma']).to.equal('no-cache');
  });

  it('should set HSTS in production', async () => {
    if (process.env.NODE_ENV === 'production') {
      const response = await request(BASE_URL).get('/dashboard');
      expect(response.headers['strict-transport-security']).to.exist;
    }
  });
});
```

### 10. AUTHENTICATION MIDDLEWARE TESTING
**Location**: `apps/frontend/src/middleware.ts`

[ ] Test Route Protection
```javascript
// Test file: tests/auth/middleware.test.js
describe('Authentication Middleware', () => {
  it('should protect authenticated routes', async () => {
    const response = await request(BASE_URL)
      .get('/dashboard')
      .set('Cookie', ''); // No auth cookie
    
    expect(response.status).to.be.oneOf([302, 401]);
  });

  it('should allow public routes', async () => {
    const response = await request(BASE_URL).get('/sign-in');
    expect(response.status).to.equal(200);
  });

  it('should handle onboarding path security', async () => {
    const response = await request(BASE_URL)
      .get('/onboarding')
      .set('Cookie', ''); // No auth cookie
    
    expect(response.status).to.be.oneOf([302, 401]);
  });
});
```

## ABDM AUTHENTICATION TESTING

### 11. ABDM TOKEN MANAGEMENT TESTING
**Location**: `apps/frontend/src/services/abdm/utils/auth.ts`

[ ] Test ABDM Authentication Security
```javascript
// Test file: tests/abdm/abdm-auth.test.js
describe('ABDM Authentication', () => {
  it('should securely manage ABDM tokens', async () => {
    // Test token generation
    // Test token caching
    // Test token expiration handling
    // Test token refresh mechanism
  });

  it('should protect ABDM client secrets', async () => {
    // Verify client secret is not exposed in logs
    // Test secure token request
    // Validate error handling doesn't expose secrets
  });

  it('should handle ABDM authentication errors', async () => {
    // Test invalid client ID
    // Test invalid client secret
    // Test network errors
    // Test timeout handling
  });
});
```

## WEBSOCKET SECURITY TESTING

### 12. SOCKET AUTHENTICATION TESTING
**Location**: `apps/frontend/src/lib/socket-client.ts`

[ ] Test WebSocket Security
```javascript
// Test file: tests/security/socket-security.test.js
describe('WebSocket Security', () => {
  it('should authenticate socket connections', async () => {
    // Test socket connection with valid auth
    // Test socket connection without auth
    // Test socket connection with invalid auth
  });

  it('should validate room access', async () => {
    // Test organization room access
    // Test branch room access
    // Test doctor room access
    // Test unauthorized room access prevention
  });

  it('should handle connection security', async () => {
    // Test connection encryption
    // Test message validation
    // Test reconnection security
    // Test connection limits
  });
});
```

## INPUT VALIDATION & XSS PREVENTION

### 13. INPUT VALIDATION TESTING

[ ] Test Comprehensive Input Validation
```javascript
// Test file: tests/security/input-validation.test.js
describe('Input Validation Security', () => {
  it('should prevent SQL injection', async () => {
    const maliciousInputs = [
      "'; DROP TABLE patients; --",
      "' OR '1'='1",
      "'; INSERT INTO users VALUES ('hacker', 'password'); --"
    ];

    for (const input of maliciousInputs) {
      const response = await request(BASE_URL)
        .get(`/api/patients/search?name=${encodeURIComponent(input)}`);
      expect(response.status).to.not.equal(500);
    }
  });

  it('should prevent XSS attacks', async () => {
    const xssPayloads = [
      '<script>alert("xss")</script>',
      '<img src="x" onerror="alert(1)">',
      'javascript:alert("xss")',
      '<svg onload="alert(1)">'
    ];

    for (const payload of xssPayloads) {
      const response = await request(BASE_URL)
        .post('/api/patients')
        .send({ name: payload, mobile: '**********' });
      
      if (response.status === 200) {
        expect(response.body.name).to.not.include('<script>');
      } else {
        expect(response.status).to.equal(400);
      }
    }
  });

  it('should validate healthcare data input', async () => {
    // Test ABHA number validation
    // Test mobile number validation
    // Test email validation
    // Test medical data validation
  });
});
```

## HEALTHCARE DATA PROTECTION TESTING

### 14. PHI PROTECTION TESTING

[ ] Test Protected Health Information Security
```javascript
// Test file: tests/security/phi-protection.test.js
describe('PHI Protection', () => {
  it('should not expose PHI in error messages', async () => {
    const response = await request(BASE_URL)
      .get('/api/patients/invalid-id');
    
    if (response.status >= 400) {
      expect(response.body.error).to.not.match(/\d{12}/); // ABHA numbers
      expect(response.body.error).to.not.match(/\d{4}-\d{4}-\d{4}/); // ABHA addresses
      expect(response.body.error).to.not.include('patient');
      expect(response.body.error).to.not.include('medical');
    }
  });

  it('should encrypt sensitive healthcare data', async () => {
    // Test patient data encryption
    // Test medical record encryption
    // Test consultation data encryption
    // Test prescription encryption
  });

  it('should audit healthcare data access', async () => {
    // Test access logging for patient data
    // Test access logging for medical records
    // Test access logging for consultations
    // Test audit trail completeness
  });
});
```

## TESTING EXECUTION PLAN

### PHASE 1: CRITICAL SECURITY FIXES (Days 1-2)
1. Test hardcoded credential removal
2. Test API key authentication
3. Test CORS configuration
4. Test environment variable security
5. Test security headers implementation

### PHASE 2: AUTHENTICATION SYSTEM (Days 3-4)
6. Test NextAuth configuration
7. Test password security
8. Test cookie security
9. Test session management
10. Test middleware protection

### PHASE 3: APPLICATION SECURITY (Days 5-6)
11. Test ABDM authentication
12. Test WebSocket security
13. Test input validation
14. Test PHI protection
15. Test healthcare data encryption

### PHASE 4: COMPREHENSIVE VALIDATION (Days 7-8)
16. Execute full security test suite
17. Perform penetration testing
18. Validate compliance requirements
19. Generate security reports
20. Prepare audit documentation

## SUCCESS CRITERIA
- [ ] All hardcoded credentials removed and tested
- [ ] API key authentication enforced and validated
- [ ] CORS properly configured and restricted
- [ ] Environment variables secured
- [ ] Security headers implemented correctly
- [ ] Authentication system fully secured
- [ ] Cookie and session security validated
- [ ] Input validation preventing all injection attacks
- [ ] PHI protection verified
- [ ] Healthcare data encryption confirmed
- [ ] Complete audit trail established
- [ ] All security tests passing

This comprehensive testing plan ensures all authentication, cookie, session, and security components are thoroughly validated for STQC compliance.
