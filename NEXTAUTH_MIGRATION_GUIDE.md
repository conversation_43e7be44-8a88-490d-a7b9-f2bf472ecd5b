# NextAuth.js Migration Guide

## Overview
This guide documents the migration from custom cookie-based authentication to NextAuth.js with database session strategy while preserving all existing authentication logic, custom user fields (role, organizationId), and role-based access control.

## ✅ Completed Migration Steps

### 1. NextAuth.js API Route Handler
- **File**: `apps/frontend/src/app/api/auth/[...nextauth]/route.ts`
- **Status**: ✅ **CONFIGURED**
- **Features**:
  - Database session strategy (`session: { strategy: "database" }`)
  - Prisma adapter for session storage
  - Custom user fields (role, organizationId) preserved
  - Real user authentication with database lookup
  - Email verification check
  - Organization-based role assignment
  - Proper TypeScript type extensions

### 2. Session Provider
- **File**: `apps/frontend/src/components/providers/session-provider.tsx`
- **Status**: ✅ **CONFIGURED**
- **Features**:
  - Properly integrated in app layout
  - Removed duplicate session providers
  - Server-side session passing

### 3. Database Configuration
- **File**: `apps/frontend/prisma/schema.prisma`
- **Status**: ✅ **CONFIGURED**
- **Features**:
  - NextAuth.js tables (Account, Session, VerificationToken) present
  - User model with custom fields (role, organizationId)
  - Proper relationships maintained

### 4. Environment Variables
- **File**: `apps/frontend/.env.example`
- **Status**: ✅ **DOCUMENTED**
- **Required Variables**:
  - `NEXTAUTH_URL`: Application URL
  - `NEXTAUTH_SECRET`: Secret key for JWT signing
  - `DATABASE_URL`: PostgreSQL connection string

### 5. Middleware Updates
- **File**: `apps/frontend/src/middleware.ts`
- **Status**: ✅ **PARTIALLY MIGRATED**
- **Features**:
  - Updated to check NextAuth.js session tokens
  - Maintains existing route protection logic
  - Preserves role-based access control

## ⚠️ Remaining Migration Tasks

### 1. **Remove Legacy Authentication Routes**
The following custom authentication routes should be **deprecated** once NextAuth.js is fully tested:

- `apps/frontend/src/app/api/login/route.ts` - Replace with NextAuth.js signIn
- `apps/frontend/src/app/api/logout/route.ts` - Replace with NextAuth.js signOut  
- `apps/frontend/src/app/api/register/route.ts` - Integrate with NextAuth.js flow

### 2. **Update Client-Side Authentication**
- Replace custom login/logout calls with NextAuth.js hooks
- Update components to use `useSession` from `next-auth/react`
- Remove custom auth context (`apps/frontend/src/contexts/auth-context.tsx`)

### 3. **Update API Route Authentication**
- Replace custom session checking with `getServerSession(authOptions)`
- Remove dependency on custom cookies (`session-token`, `user-info`)
- Update all API routes that currently use custom authentication

### 4. **Complete Middleware Migration**
- Implement proper NextAuth.js middleware with `withAuth`
- Remove fallback to custom cookie checking
- Ensure role-based routing works with NextAuth.js sessions

## 🔧 Testing Checklist

Before removing legacy authentication:

- [ ] Test login with existing user credentials
- [ ] Verify session persistence across page refreshes
- [ ] Test role-based access control
- [ ] Verify organization switching functionality
- [ ] Test logout functionality
- [ ] Verify API routes work with NextAuth.js sessions
- [ ] Test middleware route protection
- [ ] Verify custom user fields are preserved in session

## 🚀 Deployment Considerations

### Environment Variables
Ensure these are set in production:
```bash
NEXTAUTH_URL=https://your-production-domain.com
NEXTAUTH_SECRET=your-super-secure-secret-key-minimum-32-characters
DATABASE_URL=your-production-database-url
```

### Database Migration
The NextAuth.js tables are already present in your schema. No additional migrations needed.

### Session Storage
- Sessions are now stored in the database (Session table)
- Session tokens are HTTP-only cookies managed by NextAuth.js
- Default session expiry: 30 days with 24-hour update interval

## 📋 Next Steps

1. **Test the current implementation** with existing user accounts
2. **Gradually migrate client-side components** to use NextAuth.js hooks
3. **Update API routes** to use `getServerSession` instead of custom session logic
4. **Remove legacy authentication routes** once migration is complete
5. **Update middleware** to fully use NextAuth.js session checking

## 🔍 Key Benefits Achieved

- ✅ **Database session strategy** for better security and scalability
- ✅ **Preserved custom user fields** (role, organizationId)
- ✅ **Maintained role-based access control**
- ✅ **Existing authentication logic** integrated seamlessly
- ✅ **Type-safe session handling** with TypeScript
- ✅ **Production-ready configuration** with proper security settings
