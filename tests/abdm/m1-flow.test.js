/**
 * ABHA M1 Flow Testing - Aadhaar-based Registration
 * Tests the complete M1 flow for ABHA profile creation using Aadhaar
 */

const request = require('supertest');
const { expect } = require('chai');

// Test configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3005';
const TEST_AADHAAR = '123456789012'; // Test Aadhaar number
const TEST_MOBILE = '9876543210';
const TEST_OTP = '123456'; // Mock OTP for testing

describe('ABHA M1 Flow - Aadhaar Registration', () => {
  let app;
  let testTransactionId;
  let testAbhaAddress;

  before(async () => {
    // Setup test environment
    console.log('Setting up ABHA M1 Flow tests...');
  });

  describe('Step 1: Aadhaar OTP Generation', () => {
    it('should generate OTP for valid Aadhaar number', async () => {
      const response = await request(BASE_URL)
        .post('/api/abdm/abha-create/aadhaar/generate-otp')
        .send({
          aadhaar: TEST_AADHAAR,
          captcha: 'test-captcha'
        })
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body).to.have.property('transactionId');
      testTransactionId = response.body.transactionId;

      console.log('✅ Aadhaar OTP generated successfully');
    });

    it('should reject invalid Aadhaar number', async () => {
      const response = await request(BASE_URL)
        .post('/api/abdm/abha-create/aadhaar/generate-otp')
        .send({
          aadhaar: '123456789', // Invalid Aadhaar
          captcha: 'test-captcha'
        })
        .expect(400);

      expect(response.body).to.have.property('error');
      console.log('✅ Invalid Aadhaar rejected correctly');
    });
  });

  describe('Step 2: Aadhaar OTP Verification', () => {
    it('should verify OTP and create ABHA profile', async () => {
      const response = await request(BASE_URL)
        .post('/api/abdm/abha-create/aadhaar/verify-otp')
        .send({
          transactionId: testTransactionId,
          otp: TEST_OTP
        })
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body).to.have.property('abhaAddress');
      expect(response.body).to.have.property('abhaNumber');
      testAbhaAddress = response.body.abhaAddress;

      console.log('✅ Aadhaar OTP verified and ABHA profile created');
    });

    it('should reject invalid OTP', async () => {
      const response = await request(BASE_URL)
        .post('/api/abdm/abha-create/aadhaar/verify-otp')
        .send({
          transactionId: testTransactionId,
          otp: '000000' // Invalid OTP
        })
        .expect(400);

      expect(response.body).to.have.property('error');
      console.log('✅ Invalid OTP rejected correctly');
    });
  });

  describe('Step 3: Mobile Number Update', () => {
    it('should request OTP for mobile update', async () => {
      const response = await request(BASE_URL)
        .post('/api/abdm/abha-create/mobile-update/request-otp')
        .send({
          abhaAddress: testAbhaAddress,
          mobile: TEST_MOBILE
        })
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body).to.have.property('transactionId');

      console.log('✅ Mobile update OTP requested successfully');
    });

    it('should verify mobile OTP and update profile', async () => {
      const response = await request(BASE_URL)
        .post('/api/abdm/abha-create/mobile-update/verify-otp')
        .send({
          transactionId: testTransactionId,
          otp: TEST_OTP,
          mobile: TEST_MOBILE
        })
        .expect(200);

      expect(response.body).to.have.property('success', true);
      console.log('✅ Mobile number updated successfully');
    });
  });

  describe('Step 4: Profile Completion Validation', () => {
    it('should retrieve complete ABHA profile', async () => {
      const response = await request(BASE_URL)
        .get(`/api/abdm/abha-profile/get-details?abhaAddress=${testAbhaAddress}`)
        .expect(200);

      expect(response.body).to.have.property('abhaAddress', testAbhaAddress);
      expect(response.body).to.have.property('mobile', TEST_MOBILE);
      expect(response.body).to.have.property('name');

      console.log('✅ Complete ABHA profile retrieved successfully');
    });

    it('should validate profile data integrity', async () => {
      const response = await request(BASE_URL)
        .get(`/api/abdm/abha-profile/get-details?abhaAddress=${testAbhaAddress}`)
        .expect(200);

      // Validate required fields are present
      expect(response.body).to.have.property('abhaNumber');
      expect(response.body).to.have.property('abhaAddress');
      expect(response.body).to.have.property('name');
      expect(response.body).to.have.property('gender');
      expect(response.body).to.have.property('yearOfBirth');

      // Validate no sensitive data is exposed
      expect(response.body).to.not.have.property('aadhaar');
      expect(response.body).to.not.have.property('password');

      console.log('✅ Profile data integrity validated');
    });
  });

  describe('Security Validations', () => {
    it('should not expose sensitive data in responses', async () => {
      const response = await request(BASE_URL)
        .post('/api/abdm/abha-create/aadhaar/generate-otp')
        .send({
          aadhaar: TEST_AADHAAR,
          captcha: 'test-captcha'
        })
        .expect(200);

      // Ensure no sensitive data is exposed
      expect(response.body).to.not.have.property('aadhaar');
      expect(response.body).to.not.have.property('clientSecret');
      expect(response.body).to.not.have.property('accessToken');

      console.log('✅ No sensitive data exposed in M1 flow');
    });

    it('should validate input sanitization', async () => {
      const maliciousInput = '<script>alert("xss")</script>';
      const response = await request(BASE_URL)
        .post('/api/abdm/abha-create/aadhaar/generate-otp')
        .send({
          aadhaar: maliciousInput,
          captcha: 'test-captcha'
        })
        .expect(400);

      expect(response.body).to.have.property('error');
      console.log('✅ Input sanitization working correctly');
    });
  });

  after(async () => {
    console.log('ABHA M1 Flow tests completed');
  });
});