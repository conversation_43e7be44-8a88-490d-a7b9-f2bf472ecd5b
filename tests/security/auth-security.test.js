/**
 * Authentication Security Tests
 * Validates that all critical security vulnerabilities have been fixed
 */

const request = require('supertest');
const { expect } = require('chai');

const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3005';
const NODE_SERVER_URL = process.env.NODE_SERVER_URL || 'http://localhost:8000';

describe('Authentication Security Tests', () => {

  describe('Critical Vulnerability Fixes', () => {

    it('should reject hardcoded demo credentials', async () => {
      const response = await request(BASE_URL)
        .post('/api/auth/signin')
        .send({
          email: '<EMAIL>',
          password: 'demo123'
        });

      // Should be rejected after security fix
      expect(response.status).to.be.oneOf([401, 404]);
      console.log('✅ Hardcoded demo credentials properly rejected');
    });

    it('should reject hardcoded admin credentials', async () => {
      const response = await request(BASE_URL)
        .post('/api/auth/signin')
        .send({
          email: '<EMAIL>',
          password: 'admin123'
        });

      // Should be rejected after security fix
      expect(response.status).to.be.oneOf([401, 404]);
      console.log('✅ Hardcoded admin credentials properly rejected');
    });

    it('should require API key for Fidelius endpoints', async () => {
      const response = await request(NODE_SERVER_URL)
        .post('/fidelius-api/encrypt')
        .send({
          string_to_encrypt: 'test data',
          sender_nonce: 'test-nonce',
          requester_nonce: 'test-nonce',
          sender_private_key: 'test-key',
          requester_public_key: 'test-key'
        });

      // Should require API key after security fix
      expect(response.status).to.equal(401);
      expect(response.body).to.have.property('error');
      console.log('✅ API key authentication properly enforced');
    });

    it('should validate API key for Fidelius endpoints', async () => {
      const response = await request(NODE_SERVER_URL)
        .post('/fidelius-api/encrypt')
        .set('x-api-key', 'invalid-key')
        .send({
          string_to_encrypt: 'test data',
          sender_nonce: 'test-nonce',
          requester_nonce: 'test-nonce',
          sender_private_key: 'test-key',
          requester_public_key: 'test-key'
        });

      // Should reject invalid API key
      expect(response.status).to.equal(401);
      console.log('✅ Invalid API key properly rejected');
    });
  });

  describe('Input Validation Security', () => {

    it('should prevent SQL injection in patient search', async () => {
      const maliciousInput = "'; DROP TABLE patients; --";
      const response = await request(BASE_URL)
        .get(`/api/patients/search?name=${encodeURIComponent(maliciousInput)}`);

      // Should not return 500 error (which would indicate SQL injection)
      expect(response.status).to.not.equal(500);
      console.log('✅ SQL injection prevention working');
    });

    it('should sanitize XSS attempts in patient data', async () => {
      const xssPayload = '<script>alert("xss")</script>';
      const response = await request(BASE_URL)
        .post('/api/patients')
        .send({
          name: xssPayload,
          mobile: '**********',
          email: '<EMAIL>'
        });

      // Should either reject or sanitize the input
      if (response.status === 200) {
        expect(response.body.name).to.not.include('<script>');
      } else {
        expect(response.status).to.equal(400);
      }
      console.log('✅ XSS prevention working');
    });

    it('should validate file upload types', async () => {
      const response = await request(BASE_URL)
        .post('/api/azure/upload')
        .attach('file', Buffer.from('malicious content'), 'malicious.exe')
        .field('consultationId', 'test-id')
        .field('patientId', 'test-patient')
        .field('bundleType', 'test');

      // Should reject non-PDF files
      expect(response.status).to.equal(400);
      expect(response.body).to.have.property('error');
      console.log('✅ File upload validation working');
    });
  });

  describe('Security Headers Validation', () => {

    it('should include security headers in responses', async () => {
      const response = await request(BASE_URL)
        .get('/dashboard');

      // Check for security headers
      expect(response.headers).to.have.property('x-frame-options');
      expect(response.headers).to.have.property('x-content-type-options');
      expect(response.headers).to.have.property('x-xss-protection');
      expect(response.headers).to.have.property('content-security-policy');

      console.log('✅ Security headers properly set');
    });

    it('should prevent clickjacking with X-Frame-Options', async () => {
      const response = await request(BASE_URL)
        .get('/dashboard');

      expect(response.headers['x-frame-options']).to.equal('DENY');
      console.log('✅ Clickjacking protection enabled');
    });

    it('should prevent MIME sniffing', async () => {
      const response = await request(BASE_URL)
        .get('/dashboard');

      expect(response.headers['x-content-type-options']).to.equal('nosniff');
      console.log('✅ MIME sniffing protection enabled');
    });
  });

  describe('Session Security', () => {

    it('should use secure session cookies', async () => {
      const response = await request(BASE_URL)
        .post('/api/login')
        .send({
          email: '<EMAIL>',
          password: 'validpassword'
        });

      if (response.status === 200) {
        const cookies = response.headers['set-cookie'];
        if (cookies) {
          const sessionCookie = cookies.find(cookie => cookie.includes('session-token'));
          if (sessionCookie) {
            expect(sessionCookie).to.include('HttpOnly');
            expect(sessionCookie).to.include('Path=/');
          }
        }
      }
      console.log('✅ Session cookie security validated');
    });

    it('should invalidate sessions on logout', async () => {
      // First login
      const loginResponse = await request(BASE_URL)
        .post('/api/login')
        .send({
          email: '<EMAIL>',
          password: 'validpassword'
        });

      if (loginResponse.status === 200) {
        // Then logout
        const logoutResponse = await request(BASE_URL)
          .post('/api/logout');

        expect(logoutResponse.status).to.be.oneOf([200, 302]);
        console.log('✅ Session invalidation working');
      }
    });
  });

  describe('CORS Security', () => {

    it('should restrict CORS origins', async () => {
      const response = await request(NODE_SERVER_URL)
        .options('/fidelius-api/health')
        .set('Origin', 'https://malicious-site.com');

      // Should not allow arbitrary origins
      expect(response.headers['access-control-allow-origin']).to.not.equal('*');
      console.log('✅ CORS properly restricted');
    });
  });

  describe('Error Handling Security', () => {

    it('should not expose stack traces in production', async () => {
      const response = await request(BASE_URL)
        .get('/api/nonexistent-endpoint');

      // Should not expose internal error details
      expect(response.body).to.not.have.property('stack');
      expect(response.body).to.not.have.property('trace');
      console.log('✅ Error information properly sanitized');
    });

    it('should not expose database errors', async () => {
      const response = await request(BASE_URL)
        .post('/api/patients')
        .send({
          // Invalid data to trigger database error
          name: null,
          mobile: 'invalid'
        });

      if (response.status >= 400) {
        expect(response.body.error).to.not.include('database');
        expect(response.body.error).to.not.include('SQL');
        expect(response.body.error).to.not.include('Prisma');
      }
      console.log('✅ Database errors properly sanitized');
    });
  });

  describe('Healthcare Data Privacy', () => {

    it('should not expose PHI in error messages', async () => {
      const response = await request(BASE_URL)
        .get('/api/patients/invalid-id');

      if (response.status >= 400) {
        // Should not expose patient identifiers or medical data
        expect(response.body.error).to.not.match(/\d{12}/); // ABHA numbers
        expect(response.body.error).to.not.match(/\d{4}-\d{4}-\d{4}/); // ABHA addresses
      }
      console.log('✅ PHI properly protected in error responses');
    });

    it('should validate ABHA data encryption', async () => {
      const response = await request(BASE_URL)
        .post('/api/abdm/abha-create/aadhaar/generate-otp')
        .send({
          aadhaar: '123456789012',
          captcha: 'test'
        });

      // Should not return raw Aadhaar in response
      if (response.body) {
        expect(JSON.stringify(response.body)).to.not.include('123456789012');
      }
      console.log('✅ ABHA data properly encrypted');
    });
  });

  after(() => {
    console.log('🔒 All security tests completed');
  });
});