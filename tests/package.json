{"name": "arancare-security-tests", "version": "1.0.0", "description": "Comprehensive security and functional testing for AranCare healthcare application", "scripts": {"test": "mocha --recursive --timeout 30000", "test:security": "mocha security/*.test.js --timeout 30000", "test:abdm": "mocha abdm/*.test.js --timeout 30000", "test:api": "mocha api/*.test.js --timeout 30000", "test:ui": "playwright test", "test:all": "npm run test:security && npm run test:abdm && npm run test:api", "test:watch": "mocha --recursive --watch --timeout 30000", "test:coverage": "nyc mocha --recursive --timeout 30000", "test:report": "mocha --recursive --timeout 30000 --reporter mochaw<PERSON><PERSON>"}, "dependencies": {"mocha": "^10.2.0", "chai": "^4.3.10", "supertest": "^6.3.3", "playwright": "^1.40.0", "@playwright/test": "^1.40.0", "mochawesome": "^7.1.3", "nyc": "^15.1.0", "axios": "^1.6.0"}, "devDependencies": {"@types/mocha": "^10.0.6", "@types/chai": "^4.3.11", "@types/supertest": "^2.0.16"}, "mocha": {"recursive": true, "timeout": 30000, "reporter": "spec", "require": ["./test-setup.js"]}, "nyc": {"reporter": ["text", "html", "lcov"], "exclude": ["tests/**", "node_modules/**"]}}