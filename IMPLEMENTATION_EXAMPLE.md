# Implementation Example: Patients Module
## Step-by-Step Guide for Team Members

This example shows exactly how to implement the security and API standardization for the **Patients Module**. Use this as a template for all other modules.

## 📁 **Current State Analysis**

### **Files Involved**
- `components/patient-registration-form.tsx` - Patient registration UI
- `components/patient-search-banner.tsx` - Patient search functionality
- `app/api/patients/route.ts` - Existing API endpoints
- `app/api/patients/[id]/route.ts` - Individual patient operations
- `services/patient-service.ts` - Patient service logic

### **Current Issues Found**
1. **Direct fetch calls** in components
2. **No input validation** on API endpoints
3. **PHI data exposure** in error messages
4. **No rate limiting** on search endpoints
5. **ABHA integration** mixed with UI logic

## 🔧 **Step-by-Step Implementation**

### **Step 1: Create Standardized API Service (30 minutes)**

Create `apps/frontend/src/services/api/patients-api.ts`:

```typescript
import { z } from 'zod';

// Input validation schemas
const CreatePatientSchema = z.object({
  name: z.string().min(1).max(100),
  mobile: z.string().regex(/^[6-9]\d{9}$/),
  email: z.string().email().optional(),
  dateOfBirth: z.string().optional(),
  gender: z.enum(['male', 'female', 'other']).optional(),
  abhaAddress: z.string().optional(),
  abhaNumber: z.string().optional(),
});

const UpdatePatientSchema = CreatePatientSchema.partial();

const SearchPatientSchema = z.object({
  query: z.string().min(1),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(50).default(10),
});

export class PatientsAPI {
  private baseUrl = '/api/patients';

  async getPatients(params: z.infer<typeof SearchPatientSchema>) {
    // Validate input
    const validatedParams = SearchPatientSchema.parse(params);

    const searchParams = new URLSearchParams({
      query: validatedParams.query,
      page: validatedParams.page.toString(),
      limit: validatedParams.limit.toString(),
    });

    const response = await fetch(`${this.baseUrl}?${searchParams}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch patients');
    }

    return response.json();
  }

  async createPatient(data: z.infer<typeof CreatePatientSchema>) {
    // Validate input
    const validatedData = CreatePatientSchema.parse(data);

    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(validatedData),
    });

    if (!response.ok) {
      throw new Error('Failed to create patient');
    }

    return response.json();
  }

  async updatePatient(id: string, data: z.infer<typeof UpdatePatientSchema>) {
    // Validate input
    const validatedData = UpdatePatientSchema.parse(data);

    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(validatedData),
    });

    if (!response.ok) {
      throw new Error('Failed to update patient');
    }

    return response.json();
  }

  async deletePatient(id: string) {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error('Failed to delete patient');
    }

    return response.json();
  }

  async getPatientByAbhaAddress(abhaAddress: string) {
    const response = await fetch(`${this.baseUrl}/by-abha-address?abhaAddress=${abhaAddress}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch patient by ABHA address');
    }

    return response.json();
  }
}

// Export singleton instance
export const patientsAPI = new PatientsAPI();
```