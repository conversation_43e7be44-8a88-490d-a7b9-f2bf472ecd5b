# ZAP Security Rules for AranCare Healthcare Application
# Format: RULE_ID	THRESHOLD	IGNORE	URL_REGEX	PARAMETER	CWE	WASCID	DESCRIPTION

# Healthcare-specific security rules
10021	MEDIUM	IGNORE		.*	200	13	X-Content-Type-Options header missing - Healthcare apps must prevent MIME sniffing
10020	MEDIUM	IGNORE		.*	693	15	X-Frame-Options header missing - Prevent clickjacking on medical forms
10016	MEDIUM	IGNORE		.*	16	14	Web Browser XSS Protection not enabled - Critical for healthcare data
10017	MEDIUM	IGNORE		.*	16	14	Cross-domain JavaScript source file inclusion - Verify all external scripts
10019	MEDIUM	IGNORE		.*	16	14	Content-Type header missing - Ensure proper content type for medical data

# Authentication and Session Management
10112	HIGH	IGNORE		.*	287	1	Session ID in URL rewrite - Never expose session IDs in healthcare apps
10054	HIGH	IGNORE		.*	614	13	Cookie without SameSite attribute - Critical for CSRF protection
10055	HIGH	IGNORE		.*	614	13	Cookie without Secure flag - All cookies must be secure in healthcare
10056	HIGH	IGNORE		.*	614	13	<PERSON>ie without HttpOnly flag - Prevent XSS access to session cookies

# Input Validation - Critical for healthcare data
40012	HIGH	IGNORE		.*	89	20	Cross Site Scripting (Reflected) - Prevent XSS in patient data forms
40014	HIGH	IGNORE		.*	79	20	Cross Site Scripting (Persistent) - Prevent stored XSS in medical records
40018	HIGH	IGNORE		.*	89	20	SQL Injection - Critical protection for patient database
40019	HIGH	IGNORE		.*	89	20	SQL Injection (MySQL) - Specific MySQL injection protection
40020	HIGH	IGNORE		.*	89	20	SQL Injection (Oracle) - Oracle-specific injection protection
40021	HIGH	IGNORE		.*	89	20	SQL Injection (PostgreSQL) - PostgreSQL injection protection for our DB

# Information Disclosure - Critical for PHI protection
10049	HIGH	IGNORE		.*	200	13	Storable and Cacheable Content - Prevent caching of medical data
10050	HIGH	IGNORE		.*	200	13	Retrieved from Cache - Ensure no PHI is cached
10051	HIGH	IGNORE		.*	200	13	Relative Path Confusion - Prevent path traversal attacks
10052	HIGH	IGNORE		.*	200	13	X-ChromeLogger-Data header detected - Remove debug headers in production
10053	HIGH	IGNORE		.*	200	13	Apache Range Header DoS - Protect against range header attacks

# API Security - Critical for ABDM integration
90001	HIGH	IGNORE	/api/.*	.*	200	13	Insecure HTTP Method - Only allow necessary HTTP methods on APIs
90011	HIGH	IGNORE	/api/.*	.*	200	13	Charset Mismatch - Ensure consistent charset in API responses
90020	HIGH	IGNORE	/api/.*	.*	200	13	Missing Anti-CSRF Tokens - Protect all state-changing API calls
90021	HIGH	IGNORE	/api/.*	.*	200	13	Anti-CSRF Tokens Scanner - Validate CSRF token implementation
90022	HIGH	IGNORE	/api/.*	.*	200	13	Application Error Disclosure - Never expose stack traces in API responses

# File Upload Security - Critical for medical document uploads
30001	HIGH	IGNORE	/api/azure/upload.*	.*	434	20	Backup File Disclosure - Prevent access to backup files
30002	HIGH	IGNORE	/api/azure/upload.*	.*	434	20	Sensitive File Disclosure - Protect sensitive configuration files
30003	HIGH	IGNORE	/api/azure/upload.*	.*	434	20	Viewstate Scanner - Check for exposed viewstate data

# Healthcare Data Privacy Rules
100001	HIGH	IGNORE		.*	200	13	PHI Exposure in Response - Custom rule to detect PHI in responses
100002	HIGH	IGNORE		.*	200	13	Medical Record Number Exposure - Detect exposed MRNs
100003	HIGH	IGNORE		.*	200	13	ABHA Number Exposure - Protect ABHA numbers from exposure
100004	HIGH	IGNORE		.*	200	13	Patient Identifier Exposure - Protect all patient identifiers

# ABDM Integration Security
100010	HIGH	IGNORE	/api/abdm/.*	.*	200	13	ABDM Token Exposure - Protect ABDM access tokens
100011	HIGH	IGNORE	/api/abdm/.*	.*	200	13	Consent ID Exposure - Protect consent identifiers
100012	HIGH	IGNORE	/api/abdm/.*	.*	200	13	Health Information Exposure - Protect health information in transit

# Webhook Security
100020	HIGH	IGNORE	/api/webhook/.*	.*	200	13	Webhook Authentication Missing - Ensure all webhooks are authenticated
100021	HIGH	IGNORE	/api/webhook/.*	.*	200	13	Webhook Signature Validation - Validate webhook signatures
100022	HIGH	IGNORE	/api/webhook/.*	.*	200	13	Webhook Replay Attack - Protect against replay attacks