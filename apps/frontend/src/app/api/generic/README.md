# Generic API Documentation

## Overview

The Generic API provides a unified interface for CRUD operations across all data models in the AranCare system. Instead of creating separate endpoints for each model, you can use these 5 generic APIs with different DOCTYPE parameters.

## Base URL

```
/api/generic/{doctype}
```

## Supported DOCTYPEs

### Core Entities
- `patient` - Patient records
- `doctor` - Doctor profiles
- `consultation` - Medical consultations
- `appointment` - Appointment scheduling
- `organization` - Healthcare organizations
- `branch` - Organization branches
- `department` - Medical departments
- `user` - System users

### Medical Records
- `vitals` - Patient vital signs
- `prescription` - Medication prescriptions
- `clinicalNote` - Clinical notes and observations
- `diagnosticReport` - Lab reports and diagnostics
- `immunization` - Vaccination records
- `procedure` - Medical procedures
- `allergyIntolerance` - Allergy information

### Administrative
- `invoice` - Billing and invoices
- `labTestRequest` - Laboratory test requests
- `queueStatus` - Patient queue management
- `careType` - Types of care services

### ABDM/Health Records
- `abhaProfile` - ABHA profile information
- `consent` - Patient consent records
- `careContext` - Care context for ABDM
- `documentReference` - Document references
- `fhirBundle` - FHIR bundles
- `fhirResource` - FHIR resources

### Staff and Scheduling
- `staff` - Staff members
- `doctorSchedule` - Doctor schedules
- `scheduleSlot` - Schedule time slots
- `scheduleOverride` - Schedule overrides

## API Endpoints

### 1. Get Single Record (getRecord)

**Endpoint:** `GET /api/generic/{doctype}/{id}`

**Description:** Retrieve a single record by ID and DOCTYPE.

**Parameters:**
- `doctype` (path) - The document type
- `id` (path) - The record ID
- `include` (query) - Comma-separated relations to include
- `fields` (query) - Comma-separated fields to select

**Example:**
```bash
GET /api/generic/patient/123?include=abhaProfile,primaryBranch&fields=id,firstName,lastName
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "123",
    "firstName": "John",
    "lastName": "Doe",
    "abhaProfile": { ... },
    "primaryBranch": { ... }
  },
  "doctype": "patient",
  "id": "123"
}
```

### 2. Get Multiple Records (getRecords)

**Endpoint:** `GET /api/generic/{doctype}`

**Description:** Retrieve multiple records with filtering, pagination, and search.

**Parameters:**
- `doctype` (path) - The document type
- `page` (query) - Page number (default: 1)
- `limit` (query) - Records per page (default: 50, max: 100)
- `search` (query) - Search term for text fields
- `filter_{fieldName}` (query) - Filter by specific field value
- `sort_{fieldName}` (query) - Sort by field (asc|desc)
- `include` (query) - Comma-separated relations to include
- `fields` (query) - Comma-separated fields to select

**Example:**
```bash
GET /api/generic/patient?page=1&limit=20&search=john&filter_status=active&sort_createdAt=desc&include=abhaProfile
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "123",
      "firstName": "John",
      "lastName": "Doe",
      ...
    }
  ],
  "doctype": "patient",
  "pagination": {
    "page": 1,
    "limit": 20,
    "totalCount": 150,
    "totalPages": 8,
    "hasNextPage": true,
    "hasPrevPage": false
  },
  "filters": {
    "status": "active"
  },
  "search": "john"
}
```

### 3. Create Record (create)

**Endpoint:** `POST /api/generic/{doctype}`

**Description:** Create a new record.

**Parameters:**
- `doctype` (path) - The document type

**Request Body:** JSON object with record data

**Example:**
```bash
POST /api/generic/patient
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "dateOfBirth": "1990-01-01",
  "gender": "male",
  "phone": "+**********",
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "new-id-123",
    "firstName": "John",
    "lastName": "Doe",
    ...
  },
  "doctype": "patient",
  "message": "patient created successfully"
}
```

### 4. Update Record (update)

**Endpoint:** `PATCH /api/generic/{doctype}/{id}`

**Description:** Update an existing record.

**Parameters:**
- `doctype` (path) - The document type
- `id` (path) - The record ID

**Request Body:** JSON object with fields to update

**Example:**
```bash
PATCH /api/generic/patient/123
Content-Type: application/json

{
  "firstName": "Jane",
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "123",
    "firstName": "Jane",
    "lastName": "Doe",
    "email": "<EMAIL>",
    ...
  },
  "doctype": "patient",
  "id": "123",
  "message": "patient updated successfully"
}
```

### 5. Delete Record (delete)

**Endpoint:** `DELETE /api/generic/{doctype}/{id}`

**Description:** Delete a record (soft or hard delete).

**Parameters:**
- `doctype` (path) - The document type
- `id` (path) - The record ID
- `soft` (query) - Set to "true" for soft delete

**Example:**
```bash
DELETE /api/generic/patient/123?soft=true
```

**Response:**
```json
{
  "success": true,
  "doctype": "patient",
  "id": "123",
  "message": "patient soft deleted successfully",
  "softDelete": true
}
```

## Authentication

All endpoints require authentication via session cookies. The API automatically:
- Validates user authentication
- Filters data by organization
- Applies role-based access control
- Logs all activities for audit

## Error Responses

### 401 Unauthorized
```json
{
  "error": "Unauthorized"
}
```

### 400 Bad Request
```json
{
  "error": "Invalid DOCTYPE",
  "supportedTypes": ["patient", "doctor", ...]
}
```

### 404 Not Found
```json
{
  "error": "patient record not found"
}
```

### 422 Validation Error
```json
{
  "error": "Validation failed",
  "details": {
    "message": "Missing required fields",
    "missingFields": ["firstName", "lastName"]
  }
}
```

### 500 Server Error
```json
{
  "error": "Failed to fetch patient records"
}
```

## Security Features

1. **Authentication Required** - All endpoints require valid session
2. **Organization Isolation** - Data is automatically filtered by organization
3. **Soft Delete Support** - Models with status field support soft deletion
4. **Audit Logging** - All operations are logged for compliance
5. **Input Validation** - Required fields and data types are validated
6. **Rate Limiting** - Built-in protection against abuse

## Usage Examples

### Working with Patients

```javascript
// Get all patients
const patients = await fetch('/api/generic/patient?limit=50');

// Get patient with ABHA profile
const patient = await fetch('/api/generic/patient/123?include=abhaProfile');

// Create new patient
const newPatient = await fetch('/api/generic/patient', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    firstName: 'John',
    lastName: 'Doe',
    dateOfBirth: '1990-01-01',
    gender: 'male',
    phone: '+**********'
  })
});

// Update patient
const updatedPatient = await fetch('/api/generic/patient/123', {
  method: 'PATCH',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>'
  })
});

// Soft delete patient
await fetch('/api/generic/patient/123?soft=true', {
  method: 'DELETE'
});
```

### Working with Consultations

```javascript
// Get consultations for a patient
const consultations = await fetch('/api/generic/consultation?filter_patientId=123');

// Get consultation with related data
const consultation = await fetch('/api/generic/consultation/456?include=patient,doctor,vitals');

// Create consultation
const newConsultation = await fetch('/api/generic/consultation', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    patientId: '123',
    doctorId: '456',
    branchId: '789',
    consultationDate: '2024-01-15T10:00:00Z'
  })
});
```

This generic API system provides a consistent, secure, and efficient way to interact with all data models in the AranCare system while maintaining proper authentication, authorization, and audit trails.

## Testing the APIs

You can test these APIs using any HTTP client. Here are some examples using curl:

### Test Patient APIs

```bash
# Get all patients
curl -X GET "http://localhost:3000/api/generic/patient" \
  -H "Cookie: your-session-cookie"

# Get specific patient
curl -X GET "http://localhost:3000/api/generic/patient/123" \
  -H "Cookie: your-session-cookie"

# Create patient
curl -X POST "http://localhost:3000/api/generic/patient" \
  -H "Content-Type: application/json" \
  -H "Cookie: your-session-cookie" \
  -d '{
    "firstName": "Test",
    "lastName": "Patient",
    "dateOfBirth": "1990-01-01",
    "gender": "male",
    "phone": "+**********"
  }'

# Update patient
curl -X PATCH "http://localhost:3000/api/generic/patient/123" \
  -H "Content-Type: application/json" \
  -H "Cookie: your-session-cookie" \
  -d '{"email": "<EMAIL>"}'

# Soft delete patient
curl -X DELETE "http://localhost:3000/api/generic/patient/123?soft=true" \
  -H "Cookie: your-session-cookie"
```

### Test Consultation APIs

```bash
# Get consultations with filtering
curl -X GET "http://localhost:3000/api/generic/consultation?filter_patientId=123&include=patient,doctor" \
  -H "Cookie: your-session-cookie"

# Create consultation
curl -X POST "http://localhost:3000/api/generic/consultation" \
  -H "Content-Type: application/json" \
  -H "Cookie: your-session-cookie" \
  -d '{
    "patientId": "123",
    "doctorId": "456",
    "branchId": "789",
    "consultationDate": "2024-01-15T10:00:00Z"
  }'
```

## Implementation Notes

1. **Organization Filtering**: All data is automatically filtered by the user's organization
2. **Soft Delete**: Models with a `status` field support soft deletion
3. **Audit Logging**: All operations are logged with user, timestamp, and action details
4. **Validation**: Required fields are validated based on the model schema
5. **Relationships**: Use the `include` parameter to fetch related data
6. **Pagination**: Large datasets are automatically paginated
7. **Search**: Text search is available for models with searchable fields

## Error Handling

The API provides consistent error responses with appropriate HTTP status codes:
- 400: Bad Request (invalid DOCTYPE, missing data)
- 401: Unauthorized (no valid session)
- 404: Not Found (record doesn't exist)
- 409: Conflict (duplicate data)
- 422: Validation Error (missing required fields)
- 500: Server Error (database or system error)

## Security Considerations

1. All endpoints require authentication
2. Data is isolated by organization
3. Audit trails are maintained for compliance
4. Input validation prevents injection attacks
5. Rate limiting protects against abuse
6. Sensitive fields are handled appropriately
