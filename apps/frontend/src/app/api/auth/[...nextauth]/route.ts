export const maxDuration = 299;

import NextAuth, { AuthOptions, DefaultSession } from "next-auth";
import { db } from "@/lib/db";
import CredentialsProvider from "next-auth/providers/credentials";
import { comparePassword } from "@/lib/auth";

// 🔒 SECURITY: Extend NextAuth types for encrypted JWT data
declare module "next-auth" {
  interface User {
    id: string;
    role: string;
    organizationId: string;
    tenantId?: string;
    permissions?: string[];
  }

  interface Session {
    user: {
      id: string;
      role: string;
      organizationId: string;
      tenantId?: string;
      permissions?: string[];
    } & DefaultSession["user"];
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    role: string;
    organizationId: string;
    tenantId?: string;
    permissions?: string[];
    iat?: number;
    exp?: number;
  }
}

// 🔒 SECURITY: Secure cookie configuration based on environment
const isProduction = process.env.NODE_ENV === "production";
const cookiePrefix = isProduction ? "__Secure-" : "";

// Define and export auth options for reuse across the app
export const authOptions: AuthOptions = {
  debug: process.env.NODE_ENV === "development",

  // 🔒 SECURITY: JWT strategy only - no database sessions
  session: {
    strategy: "jwt",
    maxAge: 10 * 24 * 60 * 60, // 🔒 10 days maximum as required
    updateAge: 24 * 60 * 60, // Update session every 24 hours
  },

  // 🔒 SECURITY: Secure cookie configuration
  cookies: {
    sessionToken: {
      name: `${cookiePrefix}next-auth.session-token`,
      options: {
        httpOnly: true, // 🔒 CRITICAL: Prevents client-side JavaScript access
        sameSite: "lax", // 🔒 CSRF protection
        path: "/",
        secure: isProduction, // 🔒 HTTPS only in production
        domain: process.env.NEXT_COOKIES_DOMAIN || undefined, // 🔒 Domain scoping if needed
      },
    },
    callbackUrl: {
      name: `${cookiePrefix}next-auth.callback-url`,
      options: {
        httpOnly: true, // 🔒 Server-side only
        sameSite: "lax",
        path: "/",
        secure: isProduction,
        domain: process.env.NEXT_COOKIES_DOMAIN || undefined,
      },
    },
    csrfToken: {
      name: `${cookiePrefix}next-auth.csrf-token`,
      options: {
        httpOnly: true, // 🔒 Server-side only
        sameSite: "lax",
        path: "/",
        secure: isProduction,
        domain: process.env.NEXT_COOKIES_DOMAIN || undefined,
      },
    },
  },
  // 🔒 SECURITY: Minimal logging in production
  logger: {
    error(code, metadata) {
      console.error("NextAuth error:", { code, metadata });
    },
    warn(code) {
      if (process.env.NODE_ENV === "development") {
        console.warn("NextAuth warning:", code);
      }
    },
    debug(code, metadata) {
      if (process.env.NODE_ENV === "development") {
        console.log("NextAuth debug:", { code, metadata });
      }
    },
  },

  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // 🔒 SECURITY: Real database authentication
          const user = await db.user.findUnique({
            where: { email: credentials.email },
            include: {
              organizations: {
                select: {
                  organizationId: true,
                  isDefault: true,
                  roles: true,
                },
              },
            },
          });

          if (!user || !user.password) {
            return null;
          }

          // Check if email is verified
          if (!user.emailVerified) {
            throw new Error("Please verify your email before signing in");
          }

          const isPasswordValid = await comparePassword(
            credentials.password,
            user.password,
          );

          if (!isPasswordValid) {
            return null;
          }

          // Find default organization or use the first one
          const defaultOrg =
            user.organizations.find((org) => org.isDefault) ||
            user.organizations[0];

          // Get the role from the default organization
          const userRoles = Array.isArray(defaultOrg?.roles)
            ? (defaultOrg.roles as string[])
            : [];
          const primaryRole = userRoles[0] || "user";

          // 🔒 SECURITY: Return user data that will be encrypted in JWT
          return {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image,
            role: primaryRole,
            organizationId: defaultOrg?.organizationId || "",
            tenantId: defaultOrg?.organizationId, // Additional security context
            permissions: userRoles, // Store all permissions
          };
        } catch (error) {
          console.error("Error in authorize function:", error);
          return null;
        }
      },
    }),
  ],
  // 🔒 SECURITY: Secure JWT and session callbacks
  callbacks: {
    async jwt({ token, user, account }) {
      // 🔒 SECURITY: Encrypt sensitive data in JWT token
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.organizationId = user.organizationId;
        token.tenantId = user.tenantId;
        token.permissions = user.permissions;
      }

      // 🔒 SECURITY: Add timestamp for token validation
      if (account) {
        token.iat = Math.floor(Date.now() / 1000);
      }

      return token;
    },

    async session({ session, token }) {
      // 🔒 SECURITY: Only expose necessary data to client
      if (session.user && token) {
        session.user.id = token.id as string;
        session.user.role = token.role as string;
        session.user.organizationId = token.organizationId as string;
        session.user.tenantId = token.tenantId as string;
        session.user.permissions = token.permissions as string[];
      }
      return session;
    },
  },

  pages: {
    signIn: "/sign-in",
    signOut: "/sign-in",
    error: "/sign-in",
  },

  // 🔒 SECURITY: Strong secret for JWT encryption - CRITICAL!
  secret: process.env.NEXTAUTH_SECRET,
};

// Create the handler
const handler = NextAuth(authOptions);

// Export the handler functions
export const GET = handler;
export const POST = handler;
