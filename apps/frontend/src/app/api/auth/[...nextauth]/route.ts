export const maxDuration = 299;

import NextAuth, { AuthOptions, DefaultSession } from "next-auth";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { db } from "@/lib/db";
import CredentialsProvider from "next-auth/providers/credentials";
import { comparePassword } from "@/lib/auth";
import type { Adapter } from "next-auth/adapters";

// Extend the types
declare module "next-auth" {
  interface User {
    id: string;
    role: string;
    organizationId: string;
  }

  interface Session {
    user: {
      id: string;
      role: string;
      organizationId: string;
    } & DefaultSession["user"];
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    role: string;
    organizationId: string;
  }
}

// Define and export auth options for reuse across the app
export const authOptions: AuthOptions = {
  debug: process.env.NODE_ENV === "development",
  adapter: PrismaAdapter(db) as Adapter,
  logger: {
    error(code, metadata) {
      console.error("NextAuth error:", { code, metadata });
    },
    warn(code) {
      console.warn("NextAuth warning:", code);
    },
    debug(code, metadata) {
      if (process.env.NODE_ENV === "development") {
        console.log("NextAuth debug:", { code, metadata });
      }
    },
  },
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // For demo purposes, hardcode the credentials check
          if (
            credentials.email === "<EMAIL>" &&
            credentials.password === "demo123"
          ) {
            return {
              id: "demo-user-id",
              email: "<EMAIL>",
              name: "Demo User",
              role: "user",
              organizationId: "org-1",
            };
          }

          if (
            credentials.email === "<EMAIL>" &&
            credentials.password === "admin123"
          ) {
            return {
              id: "admin-user-id",
              email: "<EMAIL>",
              name: "Admin User",
              role: "admin",
              organizationId: "org-1",
            };
          }

          // Real user authentication
          const user = await db.user.findUnique({
            where: { email: credentials.email },
            include: {
              organizations: {
                select: {
                  organizationId: true,
                  isDefault: true,
                  roles: true,
                },
              },
            },
          });

          if (!user || !user.password) {
            return null;
          }

          // Check if email is verified
          if (!user.emailVerified) {
            throw new Error("Please verify your email before signing in");
          }

          const isPasswordValid = await comparePassword(
            credentials.password,
            user.password,
          );

          if (!isPasswordValid) {
            return null;
          }

          // Find default organization or use the first one
          const defaultOrg =
            user.organizations.find((org) => org.isDefault) ||
            user.organizations[0];

          // Get the role from the default organization
          const userRoles = Array.isArray(defaultOrg?.roles)
            ? (defaultOrg.roles as string[])
            : [];
          const primaryRole = userRoles[0] || "user";

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image,
            role: primaryRole,
            organizationId: defaultOrg?.organizationId || "",
          };
        } catch (error) {
          console.error("Error in authorize function:", error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async session({ session, user }) {
      if (session.user && user) {
        // Get fresh user data with organization info
        const dbUser = await db.user.findUnique({
          where: { id: user.id },
          include: {
            organizations: {
              select: {
                organizationId: true,
                isDefault: true,
                roles: true,
              },
            },
          },
        });

        if (dbUser) {
          const defaultOrg =
            dbUser.organizations.find((org) => org.isDefault) ||
            dbUser.organizations[0];

          const userRoles = Array.isArray(defaultOrg?.roles)
            ? (defaultOrg.roles as string[])
            : [];
          const primaryRole = userRoles[0] || "user";

          session.user.id = user.id;
          session.user.role = primaryRole;
          session.user.organizationId = defaultOrg?.organizationId || "";
        }
      }
      return session;
    },
  },
  pages: {
    signIn: "/sign-in",
    signOut: "/sign-in",
    error: "/sign-in",
  },
  session: {
    strategy: "database",
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },
  secret: process.env.NEXTAUTH_SECRET,
};

// Create the handler
const handler = NextAuth(authOptions);

// Export the handler functions
export const GET = handler;
export const POST = handler;
