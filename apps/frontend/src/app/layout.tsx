import * as Sentry from "@sentry/nextjs";
import type { <PERSON><PERSON><PERSON> } from "next";
import { Inter } from "next/font/google";
import { JetBrains_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { OrganizationProvider } from "@/contexts/organization-context";
import { BranchProvider } from "@/contexts/branch-context";
import { Toaster } from "sonner";
import NextAuthProvider from "@/components/providers/session-provider";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { DataDogScript } from "../../../../lib/audit/datadog";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

const jetBrainsMono = JetBrains_Mono({
  subsets: ["latin"],
  variable: "--font-geist-mono",
});

// Add or edit your "generateMetadata" to include the Sentry trace data:
export function generateMetadata(): Metadata {
  return {
    title: "Aran Care",
    description: "A modern healthcare platform for patients and providers",
    other: {
      ...Sentry.getTraceData(),
    },
  };
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getServerSession(authOptions);

  return (
    <html lang="en">
      <body
        className={`${inter.variable} ${jetBrainsMono.variable} font-sans antialiased bg-background text-foreground`}
      >
        <DataDogScript />
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
          <NextAuthProvider session={session}>
            <OrganizationProvider>
              <BranchProvider>
                <Toaster position="top-right" richColors />
                {children}
              </BranchProvider>
            </OrganizationProvider>
          </NextAuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
