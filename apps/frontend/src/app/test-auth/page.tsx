"use client";

import { useSession, signIn, signOut } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function TestAuth() {
  const { data: session, status } = useSession();

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle>NextAuth.js Test Page</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {session ? (
            <div className="space-y-4">
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <h3 className="font-semibold text-green-800">✅ Authenticated</h3>
                <p className="text-green-700">NextAuth.js session is working!</p>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-semibold">Session Data:</h4>
                <div className="bg-gray-50 p-3 rounded text-sm">
                  <p><strong>Name:</strong> {session.user?.name}</p>
                  <p><strong>Email:</strong> {session.user?.email}</p>
                  <p><strong>Role:</strong> {session.user?.role}</p>
                  <p><strong>Organization ID:</strong> {session.user?.organizationId}</p>
                </div>
              </div>

              <Button onClick={() => signOut()} variant="destructive">
                Sign Out (NextAuth.js)
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h3 className="font-semibold text-yellow-800">⚠️ Not Authenticated</h3>
                <p className="text-yellow-700">No NextAuth.js session found</p>
              </div>

              <Button onClick={() => signIn()} variant="default">
                Sign In (NextAuth.js)
              </Button>
            </div>
          )}

          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-semibold text-blue-800">Migration Status</h4>
            <div className="text-blue-700 text-sm mt-2 space-y-1">
              <p>✅ NextAuth.js API route configured</p>
              <p>✅ Session provider integrated</p>
              <p>✅ Sign-in page updated</p>
              <p>✅ User navigation updated</p>
              <p>⚠️ Legacy API routes still active</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
