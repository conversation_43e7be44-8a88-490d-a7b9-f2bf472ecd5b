"use client";

import { useSession, signIn, signOut } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function TestAuth() {
  const { data: session, status } = useSession();

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>🔒 Secure NextAuth.js Implementation Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {session ? (
            <div className="space-y-4">
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <h3 className="font-semibold text-green-800">✅ Authenticated</h3>
                <p className="text-green-700">Secure NextAuth.js session is working!</p>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-semibold">Session Data (Client-Side):</h4>
                <div className="bg-gray-50 p-3 rounded text-sm">
                  <p><strong>Name:</strong> {session.user?.name}</p>
                  <p><strong>Email:</strong> {session.user?.email}</p>
                  <p><strong>Role:</strong> {session.user?.role}</p>
                  <p><strong>Organization ID:</strong> {session.user?.organizationId}</p>
                  <p><strong>Tenant ID:</strong> {session.user?.tenantId}</p>
                </div>
              </div>

              <Button onClick={() => signOut()} variant="destructive">
                Sign Out (NextAuth.js)
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h3 className="font-semibold text-yellow-800">⚠️ Not Authenticated</h3>
                <p className="text-yellow-700">No NextAuth.js session found</p>
              </div>

              <Button onClick={() => signIn()} variant="default">
                Sign In (NextAuth.js)
              </Button>
            </div>
          )}

          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h4 className="font-semibold text-green-800">🔒 Security Status</h4>
            <div className="text-green-700 text-sm mt-2 space-y-1">
              <p>✅ JWT-only session strategy</p>
              <p>✅ httpOnly secure cookies</p>
              <p>✅ Encrypted session data</p>
              <p>✅ CSRF protection enabled</p>
              <p>✅ Environment-based security</p>
              <p>✅ No sensitive data in dev tools</p>
            </div>
          </div>

          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-semibold text-blue-800">Migration Status</h4>
            <div className="text-blue-700 text-sm mt-2 space-y-1">
              <p>✅ Secure NextAuth.js configuration</p>
              <p>✅ Session provider integrated</p>
              <p>✅ Sign-in page updated</p>
              <p>✅ User navigation updated</p>
              <p>✅ Secure session utilities created</p>
              <p>⚠️ Legacy API routes migration in progress</p>
            </div>
          </div>

          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 className="font-semibold text-yellow-800">🔍 Security Test</h4>
            <div className="text-yellow-700 text-sm mt-2 space-y-2">
              <p><strong>Test 1:</strong> Open browser dev tools → Application → Cookies</p>
              <p>✅ Verify: Cookies are httpOnly and contain encrypted JWT data only</p>
              <p><strong>Test 2:</strong> Try accessing cookies via JavaScript:</p>
              <code className="block bg-yellow-100 p-2 rounded text-xs">
                document.cookie // Should not expose sensitive data
              </code>
              <p><strong>Test 3:</strong> Check Network tab for encrypted tokens only</p>
            </div>
          </div>

          <div className="mt-4 p-4 bg-purple-50 border border-purple-200 rounded-lg">
            <h4 className="font-semibold text-purple-800">🛡️ Security Features</h4>
            <div className="text-purple-700 text-sm mt-2 grid grid-cols-1 md:grid-cols-2 gap-2">
              <div>
                <p><strong>Cookie Security:</strong></p>
                <ul className="list-disc list-inside text-xs">
                  <li>httpOnly: true</li>
                  <li>secure: true (production)</li>
                  <li>sameSite: "lax"</li>
                  <li>__Secure- prefix (production)</li>
                </ul>
              </div>
              <div>
                <p><strong>Data Protection:</strong></p>
                <ul className="list-disc list-inside text-xs">
                  <li>JWT encryption</li>
                  <li>Server-side validation</li>
                  <li>No client-side sensitive data</li>
                  <li>Automatic token rotation</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
