import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { webhookCaptureMiddleware } from "./middleware/webhook-capture";
import { adminMiddleware } from "./middleware/admin";
import { organizationStatusMiddleware } from "./middleware/organization-status";

export async function middleware(request: NextRequest) {
  // Apply webhook capture middleware for webhook routes
  if (request.nextUrl.pathname.includes("/api/webhook/")) {
    const response = NextResponse.next();
    return await webhookCaptureMiddleware(request, response);
  }

  // Apply admin middleware for admin routes
  if (request.nextUrl.pathname.startsWith("/admin")) {
    const adminResult = adminMiddleware(request);
    // If admin middleware returns a redirect, return it immediately
    if (adminResult.status === 307 || adminResult.status === 302) {
      return adminResult;
    }
    // If admin middleware allows access, skip the rest of the middleware
    return NextResponse.next();
  }

  // Allow access to the deactivation page itself
  if (request.nextUrl.pathname.startsWith("/organization-deactivated")) {
    return NextResponse.next();
  }

  const path = request.nextUrl.pathname;

  // Define public paths that don't require authentication
  const isPublicPath =
    path === "/sign-in" ||
    path === "/signup" ||
    path.startsWith("/verify") ||
    path.startsWith("/doctor-invitation");

  // Check if user is authenticated using NextAuth.js session
  const nextAuthSessionToken = request.cookies.get("next-auth.session-token") ||
                               request.cookies.get("__Secure-next-auth.session-token");

  // Check if user is super admin and trying to access main app routes
  if (nextAuthSessionToken) {
    // For now, we'll need to get user role from a different approach
    // since we can't easily decode the session token in middleware
    // We'll use a fallback to the existing cookie approach during migration
    const currentRoleCookie = request.cookies.get("current-role");
    const userInfoCookie = request.cookies.get("user-info");

    let userRole = null;

    if (currentRoleCookie) {
      userRole = currentRoleCookie.value;
    } else if (userInfoCookie) {
      try {
        const userInfo = JSON.parse(decodeURIComponent(userInfoCookie.value));
        userRole = userInfo.role;
      } catch (error) {
        console.error("Error parsing user info cookie:", error);
      }
    }

    // Keep admin protection - only super admins can access admin routes
    if (userRole !== "superAdmin" && path.startsWith("/admin")) {
      const url = request.nextUrl.clone();
      url.pathname = "/dashboard";
      return NextResponse.redirect(url);
    }

    // Remove the super admin restriction - let them access main app routes too

    // Check organization status for non-super admin users
    if (
      userRole !== "superAdmin" &&
      !path.startsWith("/api/") &&
      !path.startsWith("/_next") &&
      !isPublicPath
    ) {
      const orgStatusResult = await organizationStatusMiddleware(request);
      if (
        orgStatusResult &&
        (orgStatusResult.status === 307 || orgStatusResult.status === 302)
      ) {
        return orgStatusResult;
      }
    }
  }

  // Define paths that require authentication but are not protected
  const isAuthPath = path === "/onboarding";

  // Check if user is authenticated with NextAuth.js
  const isAuthenticated = !!nextAuthSessionToken;

  // Check if user has completed onboarding
  // We'll check this by making a request to our API endpoint
  let hasCompletedOnboarding = false;

  // Skip onboarding check for API routes and public paths
  const shouldCheckOnboarding =
    isAuthenticated &&
    !path.startsWith("/api/") &&
    !isPublicPath &&
    path !== "/onboarding";

  if (shouldCheckOnboarding) {
    try {
      // Make a request to our API endpoint to check onboarding status
      // This is a server-side API that queries the database directly
      const response = await fetch(
        `${request.nextUrl.origin}/api/auth/onboarding-status`,
        {
          headers: {
            cookie: request.headers.get("cookie") || "",
          },
        },
      );

      if (response.ok) {
        const data = await response.json();
        hasCompletedOnboarding = data.completed;
      }
    } catch (error) {
      console.error("Error checking onboarding status:", error);
    }
  }

  // Redirect logic
  if (isPublicPath && isAuthenticated) {
    // Special handling for verification and doctor invitation pages - never redirect away from these
    if (path.startsWith("/verify") || path.startsWith("/doctor-invitation")) {
      return NextResponse.next();
    }

    // If user is authenticated and tries to access login/signup pages, redirect to dashboard or onboarding
    if (!hasCompletedOnboarding) {
      return NextResponse.redirect(new URL("/onboarding", request.url));
    }
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }

  if (
    !isPublicPath &&
    !isAuthPath &&
    !isAuthenticated &&
    !path.includes("/_next") &&
    !path.includes("/api")
  ) {
    // If user is not authenticated and tries to access protected routes, redirect to login
    return NextResponse.redirect(new URL("/sign-in", request.url));
  }

  // Force authenticated users to complete onboarding
  if (
    isAuthenticated &&
    !hasCompletedOnboarding &&
    path !== "/onboarding" &&
    !path.includes("/_next") &&
    !path.includes("/api") &&
    !path.startsWith("/verify") &&
    !path.startsWith("/doctor-invitation")
  ) {
    // If user is authenticated but hasn't completed onboarding, redirect to onboarding
    return NextResponse.redirect(new URL("/onboarding", request.url));
  }

  // Special handling for onboarding path
  if (isAuthPath && !isAuthenticated) {
    // If user is not authenticated and tries to access onboarding, redirect to login
    return NextResponse.redirect(new URL("/sign-in", request.url));
  }

  return NextResponse.next();
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!_next/static|_next/image|favicon.ico).*)",
  ],
};
