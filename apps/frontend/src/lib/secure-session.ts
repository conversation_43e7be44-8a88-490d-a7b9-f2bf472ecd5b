/**
 * 🔒 SECURE SESSION UTILITIES
 * 
 * This module provides secure session handling utilities that prevent
 * sensitive data exposure in browser developer tools.
 * 
 * SECURITY PRINCIPLES:
 * 1. All sensitive data is encrypted in JWT tokens
 * 2. No sensitive data in client-accessible cookies
 * 3. Server-side session validation only
 * 4. Proper error handling without data leakage
 */

import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { redirect } from "next/navigation";

// 🔒 SECURITY: Type definitions for secure session data
export interface SecureUser {
  id: string;
  email: string;
  name: string | null;
  role: string;
  organizationId: string;
  tenantId?: string;
  permissions?: string[];
}

export interface SecureSession {
  user: SecureUser;
  expires: string;
}

/**
 * 🔒 SECURITY: Get authenticated session server-side only
 * This function ensures sensitive data never reaches the client
 */
export async function getSecureSession(): Promise<SecureSession | null> {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return null;
    }

    // 🔒 SECURITY: Return only necessary data, all encrypted in JWT
    return {
      user: {
        id: session.user.id,
        email: session.user.email || "",
        name: session.user.name,
        role: session.user.role,
        organizationId: session.user.organizationId,
        tenantId: session.user.tenantId,
        permissions: session.user.permissions || [],
      },
      expires: session.expires,
    };
  } catch (error) {
    console.error("🔒 Secure session error:", error);
    return null;
  }
}

/**
 * 🔒 SECURITY: Require authentication with automatic redirect
 * Use this in server components that require authentication
 */
export async function requireAuth(): Promise<SecureSession> {
  const session = await getSecureSession();
  
  if (!session) {
    redirect("/sign-in");
  }
  
  return session;
}

/**
 * 🔒 SECURITY: Require specific role with automatic redirect
 * Use this for role-based access control
 */
export async function requireRole(allowedRoles: string[]): Promise<SecureSession> {
  const session = await requireAuth();
  
  if (!allowedRoles.includes(session.user.role)) {
    redirect("/unauthorized");
  }
  
  return session;
}

/**
 * 🔒 SECURITY: Check if user has specific permission
 * Use this for granular permission checking
 */
export async function hasPermission(permission: string): Promise<boolean> {
  const session = await getSecureSession();
  
  if (!session) {
    return false;
  }
  
  return session.user.permissions?.includes(permission) || false;
}

/**
 * 🔒 SECURITY: Get user organization context securely
 * Returns organization data without exposing sensitive information
 */
export async function getSecureOrganizationContext(): Promise<{
  organizationId: string;
  tenantId?: string;
  role: string;
} | null> {
  const session = await getSecureSession();
  
  if (!session) {
    return null;
  }
  
  return {
    organizationId: session.user.organizationId,
    tenantId: session.user.tenantId,
    role: session.user.role,
  };
}

/**
 * 🔒 SECURITY: Validate session and return user ID only
 * Use this when you only need the user ID for database queries
 */
export async function getSecureUserId(): Promise<string | null> {
  const session = await getSecureSession();
  return session?.user.id || null;
}

/**
 * 🔒 SECURITY: Check if current user is admin
 * Use this for admin-specific functionality
 */
export async function isAdmin(): Promise<boolean> {
  const session = await getSecureSession();
  return session?.user.role === "admin" || session?.user.role === "superAdmin" || false;
}

/**
 * 🔒 SECURITY: Check if current user is super admin
 * Use this for super admin-specific functionality
 */
export async function isSuperAdmin(): Promise<boolean> {
  const session = await getSecureSession();
  return session?.user.role === "superAdmin" || false;
}

// 🔒 SECURITY: Export types for use in other modules
export type { SecureUser, SecureSession };
