import { compare, hash as bcryptHash } from "bcryptjs";

export async function hashPassword(password: string) {
  return await bcryptHash(password, 10);
}

export async function comparePassword(
  password: string,
  hashedPassword: string,
) {
  try {
    const result = await compare(password, hashedPassword);
    return result;
  } catch (error) {
    console.error("Error comparing passwords:", error);
    return false;
  }
}

// Re-export authOptions from the NextAuth route handler
// This ensures we have a single source of truth for auth configuration
export { authOptions } from "@/app/api/auth/[...nextauth]/route";
