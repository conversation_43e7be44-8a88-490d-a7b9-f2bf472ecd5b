# AranCare Security & Testing Sprint PRD
## 2-3 Day Application-Specific STQC Compliance Implementation

### Executive Summary
This PRD provides a **module-by-module approach** for implementing STQC security compliance in the AranCare healthcare application. Based on actual codebase analysis, this plan focuses on **frontend-only changes**, creates standardized APIs for each module, separates client/server logic, and maintains **100% ABDM functionality**.

### Project Scope
- **Application**: AranCare Healthcare Web Application (Frontend Only)
- **Technology Stack**: Next.js 14, PostgreSQL, Azure Blob Storage, ABDM Integration
- **Approach**: Module-by-module security implementation with standardized APIs
- **Timeline**: 2-3 days (48-72 hours)
- **Constraints**:
  - **NO NODE-SERVER CHANGES**
  - **NO ABDM API CHANGES** - All ABDM flows remain exactly the same
  - **NO BREAKING CHANGES** to existing functionality

## 📋 **ARANCARE MODULE ANALYSIS & SECURITY IMPLEMENTATION**

Based on codebase analysis, here are the **14 core modules** that need security implementation:

### **Core Healthcare Modules**
1. **Patients Module** (`components/patients`, `services/patient-service.ts`)
2. **Consultations Module** (`components/consultations`, `app/api/consultations`)
3. **Doctors Module** (`components/doctor`, `app/api/doctors`)
4. **Health Records Module** (`components/health-records`, `services/health-record-service.ts`)
5. **ABDM Integration Module** (`services/abdm`, `app/api/abdm`) - **NO CHANGES**

### **Clinical Data Modules**
6. **Vitals Module** (`components/vitals`, `app/api/vitals`)
7. **Prescriptions Module** (`components/prescriptions`, `app/api/prescriptions`)
8. **Lab Reports Module** (`components/lab-reports`, `app/api/lab-test-requests`)
9. **Discharge Summary Module** (`components/discharge-summary`, `app/api/discharge-summary`)
10. **Immunization Module** (`components/immunization`, `app/api/immunization`)

### **Administrative Modules**
11. **Branches Module** (`components/branches`, `app/api/branches`)
12. **Queue Management Module** (`components/queue`, `app/api/queue`)
13. **Invoices Module** (`components/invoices`, `app/api/invoices`)
14. **File Upload Module** (`app/api/azure`, `components/azure-fhir-upload`)

## 🎯 **STANDARDIZED API PATTERN FOR EACH MODULE**

For each module, we'll create **4 standard APIs** replacing direct fetch calls:

### **Standard API Pattern**
```typescript
// For each module (e.g., patients, consultations, etc.)
GET    /api/[module]           // List/Search
POST   /api/[module]           // Create
PUT    /api/[module]/[id]      // Update
DELETE /api/[module]/[id]      // Delete
```

### **Client/Server Separation Pattern**
```typescript
// Client-side (components)
const usePatients = () => {
  // Only UI logic, no direct API calls
  return { patients, loading, error, createPatient, updatePatient }
}

// Server-side (services)
class PatientService {
  async getPatients() { /* API call logic */ }
  async createPatient() { /* API call logic */ }
  async updatePatient() { /* API call logic */ }
  async deletePatient() { /* API call logic */ }
}
```

## 📅 **2-3 DAY MODULE-BY-MODULE IMPLEMENTATION PLAN**

### **DAY 1: Core Security + High-Priority Modules (8 hours)**

#### **Morning (4 hours): Security Foundation**
1. **Authentication Fix** (1 hour)
   - Remove hardcoded credentials from NextAuth
   - Implement proper database authentication
   - Test login functionality

2. **Environment Variables Security** (1 hour)
   - Move ABDM client secrets to server-only
   - Update all service references
   - Test ABDM integration (ensure no breaking changes)

3. **Security Headers Implementation** (1 hour)
   - Add comprehensive security headers middleware
   - Configure CSP, HSTS, X-Frame-Options
   - Test header implementation

4. **Input Validation Framework** (1 hour)
   - Create Zod validation schemas
   - Implement sanitization utilities
   - Set up validation middleware

#### **Afternoon (4 hours): High-Priority Modules**

**Module 1: Patients Module** (2 hours)
- **Current State**: Direct fetch calls in components
- **Implementation**:
  ```typescript
  // Create: apps/frontend/src/services/api/patients-api.ts
  export class PatientsAPI {
    async getPatients(params) { /* GET /api/patients */ }
    async createPatient(data) { /* POST /api/patients */ }
    async updatePatient(id, data) { /* PUT /api/patients/[id] */ }
    async deletePatient(id) { /* DELETE /api/patients/[id] */ }
  }

  // Update: components/patient-registration-form.tsx
  // Replace direct fetch with service calls
  ```
- **Security**: Input validation, PHI protection, ABHA integration security
- **Testing**: Patient registration, search, ABHA linking flows

**Module 2: Consultations Module** (2 hours)
- **Current State**: Complex consultation workflow with multiple API calls
- **Implementation**:
  ```typescript
  // Create: apps/frontend/src/services/api/consultations-api.ts
  export class ConsultationsAPI {
    async getConsultations(params) { /* GET /api/consultations */ }
    async createConsultation(data) { /* POST /api/consultations */ }
    async updateConsultation(id, data) { /* PUT /api/consultations/[id] */ }
    async completeConsultation(id) { /* PUT /api/consultations/[id]/complete */ }
  }
  ```
- **Security**: Medical data protection, consultation privacy
- **Testing**: Complete consultation workflow, vitals, prescriptions

### **DAY 2: Clinical Data Modules + Testing (8 hours)**

#### **Morning (4 hours): Clinical Data Modules**

**Module 3: Vitals Module** (1 hour)
- **Files**: `components/consultations/vitals-form.tsx`, `app/api/vitals`
- **Implementation**: Standardize vitals API, add validation
- **Security**: Medical data validation, range checking

**Module 4: Prescriptions Module** (1 hour)
- **Files**: `components/consultations/prescription-form.tsx`, `app/api/prescriptions`
- **Implementation**: Secure prescription handling
- **Security**: Drug validation, dosage verification

**Module 5: Lab Reports Module** (1 hour)
- **Files**: `components/lab-reports`, `app/api/lab-test-requests`
- **Implementation**: Lab data security, file upload validation
- **Security**: Medical report protection, file type validation

**Module 6: Health Records Module** (1 hour)
- **Files**: `components/health-records`, `services/health-record-service.ts`
- **Implementation**: FHIR bundle security, ABDM integration protection
- **Security**: Health record encryption, access control

#### **Afternoon (4 hours): Automated Testing Implementation**

**ABDM Flow Testing** (2 hours)
- **M1 Flow**: Complete Aadhaar-based registration testing
- **M2 Flow**: Document-based registration testing
- **M3 Flow**: Username/password registration testing
- **Security**: PHI protection, data encryption validation

**Module-Specific Testing** (2 hours)
- **Patients Module**: Registration, search, ABHA linking
- **Consultations Module**: Complete workflow testing
- **Clinical Data**: Vitals, prescriptions, lab reports
- **Security Testing**: Input validation, XSS prevention, SQL injection

### **DAY 3: Administrative Modules + Final Validation (8 hours)**

#### **Morning (4 hours): Administrative Modules**

**Module 7: Doctors Module** (1 hour)
- **Files**: `components/doctor`, `app/api/doctors`
- **Implementation**: Doctor profile security, schedule protection

**Module 8: Branches Module** (1 hour)
- **Files**: `components/branches`, `app/api/branches`
- **Implementation**: Multi-branch security, facility data protection

**Module 9: Queue Management** (1 hour)
- **Files**: `components/queue`, `app/api/queue`
- **Implementation**: Real-time queue security, patient privacy

**Module 10: File Upload Security** (1 hour)
- **Files**: `app/api/azure`, `components/azure-fhir-upload`
- **Implementation**: Secure file handling, virus scanning, type validation

#### **Afternoon (4 hours): Final Validation & Documentation**

**Comprehensive Testing** (2 hours)
- Run all module tests
- Security vulnerability scanning
- Performance validation
- ABDM integration verification

**STQC Compliance Documentation** (1 hour)
- Security implementation documentation
- Test coverage reports
- Compliance checklist completion

**Final Deployment Preparation** (1 hour)
- Environment configuration
- Security headers validation
- Production readiness check

## 🔧 **ENVIRONMENT SETUP & CONFIGURATION**

### **Required Environment Variables**
You mentioned you don't have API keys/secrets. Here's what you need and where to get them:

#### **Existing Variables (Already in your .env)**
```bash
# Database
DATABASE_URL="your-postgresql-connection-string"

# NextAuth
NEXTAUTH_SECRET="your-nextauth-secret"
NEXTAUTH_URL="http://localhost:3005"

# ABDM (Already configured - NO CHANGES)
NEXT_PUBLIC_ABDM_CLIENT_ID="your-abdm-client-id"
ABDM_CLIENT_SECRET="your-abdm-client-secret"  # Moved from NEXT_PUBLIC_
ABDM_HIP_ID="your-hip-id"
ABDM_CM_ID="your-cm-id"

# Azure Storage (Already configured)
AZURE_STORAGE_CONNECTION_STRING="your-azure-connection"
AZURE_STORAGE_CONTAINER_NAME="your-container-name"
NEXT_PUBLIC_AZURE_STORAGE_ACCOUNT_NAME="your-account-name"
```

#### **New Variables Needed (Generate these)**
```bash
# API Security (Generate a random 32-character string)
API_KEY="generate-random-32-char-string-for-internal-apis"

# Session Security (Generate a random 64-character string)
SESSION_SECRET="generate-random-64-char-string-for-sessions"
```

### **How to Generate Missing Secrets**
```bash
# Generate API_KEY
node -e "console.log(require('crypto').randomBytes(16).toString('hex'))"

# Generate SESSION_SECRET
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

## 🧪 **APPLICATION-SPECIFIC AUTOMATED TESTING**

### **Module-Specific Test Structure**
```
tests/
├── modules/
│   ├── patients.test.js          # Patient registration, search, ABHA linking
│   ├── consultations.test.js     # Complete consultation workflow
│   ├── vitals.test.js            # Vitals recording and validation
│   ├── prescriptions.test.js     # Prescription creation and security
│   ├── lab-reports.test.js       # Lab test requests and results
│   ├── health-records.test.js    # FHIR bundles and health records
│   ├── doctors.test.js           # Doctor profiles and schedules
│   ├── branches.test.js          # Multi-branch functionality
│   ├── queue.test.js             # Queue management and real-time updates
│   └── file-upload.test.js       # Azure file upload security
├── abdm/
│   ├── m1-flow.test.js           # Aadhaar-based registration (EXISTING)
│   ├── m2-flow.test.js           # Document-based registration
│   ├── m3-flow.test.js           # Username/password registration
│   └── abdm-integration.test.js  # ABDM API integration tests
├── security/
│   ├── auth-security.test.js     # Authentication security (EXISTING)
│   ├── input-validation.test.js  # XSS, SQL injection prevention
│   ├── phi-protection.test.js    # Healthcare data privacy
│   └── api-security.test.js      # API endpoint security
└── ui/
    ├── patient-flows.spec.js     # Playwright UI tests
    ├── consultation-flows.spec.js # Doctor consultation UI
    └── admin-flows.spec.js       # Administrative UI flows
```

### **Testing Libraries Analysis**

#### **What Standard Libraries CAN'T Test (We Need Custom Scripts)**
1. **ABDM M1/M2/M3 Flows** - Healthcare-specific registration flows
2. **PHI Protection** - Healthcare data privacy validation
3. **FHIR Bundle Security** - Medical record format validation
4. **Multi-tenant Security** - Branch/organization isolation
5. **Real-time Queue Security** - WebSocket security for patient queues
6. **Azure Medical File Upload** - Healthcare document security
7. **ABHA Integration Security** - Government health ID validation

#### **What Standard Libraries CAN Test (Use Existing)**
1. **Basic API Testing** - Use Supertest, Jest
2. **UI Testing** - Use Playwright, Cypress
3. **Unit Testing** - Use Jest, Vitest
4. **Security Scanning** - Use OWASP ZAP, Snyk
5. **Performance Testing** - Use Artillery, k6

## 📋 **WORK DISTRIBUTION STRATEGY**

### **Team Member 1: Core Security & Authentication**
- **Day 1**: Authentication fixes, environment security, security headers
- **Day 2**: Input validation framework, security testing setup
- **Day 3**: Final security validation, penetration testing

### **Team Member 2: High-Priority Modules (Patients, Consultations)**
- **Day 1**: Patients module API standardization and security
- **Day 2**: Consultations module workflow security
- **Day 3**: Integration testing, UI testing setup

### **Team Member 3: Clinical Data Modules**
- **Day 1**: Vitals, Prescriptions, Lab Reports modules
- **Day 2**: Health Records, FHIR bundle security
- **Day 3**: Clinical data testing, PHI protection validation

### **Team Member 4: Administrative & Testing**
- **Day 1**: Doctors, Branches, Queue modules
- **Day 2**: ABDM flow testing (M1/M2/M3), custom test scripts
- **Day 3**: File upload security, comprehensive testing

## ✅ **SUCCESS CRITERIA & VALIDATION**

### **Module Completion Checklist**
For each of the 14 modules:
- [ ] Standardized API pattern implemented (GET, POST, PUT, DELETE)
- [ ] Client/server logic separated
- [ ] Input validation with Zod schemas
- [ ] Security headers implemented
- [ ] PHI protection validated
- [ ] Automated tests created
- [ ] No breaking changes to existing functionality

### **ABDM Integration Validation**
- [ ] All M1/M2/M3 flows work exactly as before
- [ ] ABHA registration/login unchanged
- [ ] Health record uploads functioning
- [ ] Consent management working
- [ ] Webhook handlers operational

### **Security Validation**
- [ ] No hardcoded credentials
- [ ] Environment variables secured
- [ ] Input validation preventing XSS/SQL injection
- [ ] Security headers properly configured
- [ ] PHI data protected in all responses
- [ ] File uploads secured and validated

---

**Ready to Start Implementation?**

1. **Set up environment variables** using the guide above
2. **Choose your module assignment** from the work distribution
3. **Start with Day 1 tasks** for your assigned area
4. **Use the standardized API pattern** for consistency
5. **Test each module** as you complete it

This approach ensures **zero breaking changes** while systematically securing each part of your healthcare application!

## 🧪 **APPLICATION-SPECIFIC AUTOMATED TESTING IMPLEMENTATION**

### **ABDM/ABHA Flow Testing Scripts**

#### **M1 Flow (Aadhaar-based Registration)**
```javascript
// tests/abdm/m1-flow.test.js
describe('ABHA M1 Flow - Aadhaar Registration', () => {
  test('Complete M1 flow with Aadhaar OTP', async () => {
    // Test existing endpoints:
    // POST /api/abdm/abha-create/aadhaar/generate-otp
    // POST /api/abdm/abha-create/aadhaar/verify-otp
    // POST /api/abdm/abha-create/mobile-update/request-otp
    // POST /api/abdm/abha-create/mobile-update/verify-otp
  });
});
```

#### **M2 Flow (Document-based Registration)**
```javascript
// tests/abdm/m2-flow.test.js
describe('ABHA M2 Flow - Document Registration', () => {
  test('Complete M2 flow with driving license', async () => {
    // Test existing endpoints:
    // POST /api/abdm/abha-create/driving-license/verify
    // POST /api/abdm/abha-create/pan-card/verify
  });
});
```

#### **M3 Flow (Username/Password)**
```javascript
// tests/abdm/m3-flow.test.js
describe('ABHA M3 Flow - Username Registration', () => {
  test('Complete M3 flow with username creation', async () => {
    // Test existing endpoints:
    // POST /api/abdm/abha-create/username/create
    // POST /api/abdm/abha-login/username/authenticate
  });
});
```

### **Healthcare-Specific API Testing**

#### **Patient Management APIs**
```javascript
// tests/api/patients.test.js
describe('Patient Management APIs', () => {
  test('Patient registration with ABHA integration', async () => {
    // POST /api/patients - Create patient
    // GET /api/patients/by-abha-address - Find by ABHA
    // PUT /api/patients/[id] - Update patient
  });

  test('Patient search with PHI protection', async () => {
    // Ensure no PHI exposure in search results
    // Test pagination and filtering
  });
});
```

#### **Consultation Workflow APIs**
```javascript
// tests/api/consultations.test.js
describe('Consultation Workflow', () => {
  test('Complete consultation flow', async () => {
    // POST /api/consultations - Create consultation
    // POST /api/vitals - Add vitals
    // POST /api/prescriptions - Add prescription
    // POST /api/discharge-summary - Create discharge summary
  });
});
```

#### **ABDM Integration APIs**
```javascript
// tests/api/abdm-integration.test.js
describe('ABDM Integration APIs', () => {
  test('Health record upload flows', async () => {
    // POST /api/abdm/upload-vitals
    // POST /api/abdm/upload-consultation-bundles
    // POST /api/abdm/upload-discharge-summary
    // POST /api/abdm/upload-lab-reports
  });

  test('Consent management', async () => {
    // POST /api/abdm/consent/request
    // GET /api/abdm/consent/status
    // POST /api/abdm/consent/approve
  });

  test('Webhook handlers', async () => {
    // POST /api/webhook/api/v3/hip/health-information/request
    // POST /api/abdm/webhook/v0.5/health-information/request
  });
});
```

### **UI Testing with Playwright**

#### **Healthcare User Journeys**
```javascript
// tests/ui/healthcare-flows.spec.js
import { test, expect } from '@playwright/test';

test.describe('Healthcare User Journeys', () => {
  test('Patient registration and ABHA linking', async ({ page }) => {
    // Navigate to patient registration
    await page.goto('/patients/new');

    // Fill patient details
    await page.fill('[data-testid="patient-name"]', 'Test Patient');
    await page.fill('[data-testid="patient-mobile"]', '**********');

    // Link ABHA profile
    await page.click('[data-testid="link-abha"]');
    await page.fill('[data-testid="abha-address"]', 'testpatient@sbx');

    // Verify successful registration
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
  });

  test('Doctor consultation workflow', async ({ page }) => {
    // Login as doctor
    await page.goto('/sign-in');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'securepassword');
    await page.click('[data-testid="login-button"]');

    // Start consultation
    await page.goto('/consultations/new');
    await page.selectOption('[data-testid="patient-select"]', 'patient-id');

    // Add vitals
    await page.fill('[data-testid="blood-pressure"]', '120/80');
    await page.fill('[data-testid="heart-rate"]', '72');

    // Add prescription
    await page.click('[data-testid="add-prescription"]');
    await page.fill('[data-testid="medicine-name"]', 'Paracetamol');

    // Complete consultation
    await page.click('[data-testid="complete-consultation"]');
    await expect(page.locator('[data-testid="consultation-completed"]')).toBeVisible();
  });
});
```

### **Security Testing Scripts**

#### **Authentication Security Tests**
```javascript
// tests/security/auth.test.js
describe('Authentication Security', () => {
  test('Prevent hardcoded credential access', async () => {
    const response = await request(app)
      .post('/api/auth/signin')
      .send({
        email: '<EMAIL>',
        password: 'demo123'
      });

    expect(response.status).toBe(401); // Should be rejected after fix
  });

  test('API key authentication required', async () => {
    const response = await request(app)
      .post('/fidelius-api/encrypt')
      .send({ data: 'test' });

    expect(response.status).toBe(401); // Should require API key
  });
});
```

#### **Input Validation Tests**
```javascript
// tests/security/input-validation.test.js
describe('Input Validation Security', () => {
  test('SQL injection prevention', async () => {
    const maliciousInput = "'; DROP TABLE patients; --";
    const response = await request(app)
      .get(`/api/patients/search?name=${maliciousInput}`);

    expect(response.status).toBe(400); // Should be rejected
  });

  test('XSS prevention in patient data', async () => {
    const xssPayload = '<script>alert("xss")</script>';
    const response = await request(app)
      .post('/api/patients')
      .send({ name: xssPayload });

    expect(response.status).toBe(400); // Should be sanitized/rejected
  });
});
```

## 📋 **IMPLEMENTATION CHECKLIST**

### **Day 1 Deliverables**
- [ ] Remove hardcoded credentials from NextAuth
- [ ] Enable API key authentication in node-server
- [ ] Fix CORS configuration
- [ ] Secure environment variables
- [ ] Add input validation to critical endpoints
- [ ] Implement security headers
- [ ] Test all authentication flows

### **Day 2 Deliverables**
- [ ] Complete ABDM M1/M2/M3 test suites
- [ ] API testing for all 50+ endpoints
- [ ] UI testing for healthcare workflows
- [ ] Security testing scripts
- [ ] Performance testing setup
- [ ] Error scenario testing

### **Day 3 Deliverables**
- [ ] Database security review
- [ ] File upload security hardening
- [ ] Rate limiting implementation
- [ ] Comprehensive audit logging
- [ ] Security scan execution
- [ ] STQC compliance documentation
- [ ] Final end-to-end validation

## 🎯 **SUCCESS CRITERIA**

### **Security Metrics**
- Zero critical vulnerabilities in security scan
- All authentication flows working securely
- No hardcoded credentials or exposed secrets
- All APIs properly validated and protected
- Security headers implemented correctly

### **Testing Coverage**
- 100% ABDM/ABHA flow coverage (M1, M2, M3)
- 95%+ API endpoint coverage
- Critical healthcare user journey coverage
- Security test coverage for all identified vulnerabilities
- Performance benchmarks established

### **Functional Validation**
- **ZERO BREAKING CHANGES** - All existing functionality works
- All ABDM integrations continue to work
- Patient registration and consultation flows intact
- File upload and Azure integration working
- Database operations functioning correctly

---
**Timeline**: 2-3 Days
**Owner**: Development Team
**Validation**: Security Team + QA Team
**Go-Live**: Post successful STQC audit

## Automated Testing Implementation

### Security Testing Pipeline Architecture
```yaml
# .github/workflows/security-pipeline.yml
name: Security Testing Pipeline
on: [push, pull_request]
jobs:
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - name: SAST Scan
      - name: Dependency Check
      - name: Container Security Scan
      - name: Infrastructure as Code Scan
      - name: DAST Scan (staging)
      - name: Compliance Check
```

### Testing Tools Integration
1. **Pre-commit Hooks**: Security linting, secret detection
2. **CI/CD Pipeline**: Automated security testing at every stage
3. **Staging Environment**: Full security testing before production
4. **Production Monitoring**: Continuous security monitoring

## Risk Assessment & Mitigation

### High-Risk Areas Identified
1. **Authentication System**: Hardcoded credentials, weak session management
2. **API Security**: Missing rate limiting, insufficient validation
3. **Data Exposure**: Debug information, error message disclosure
4. **Infrastructure**: Open CORS, disabled authentication

### Mitigation Strategies
- Immediate security patches for critical vulnerabilities
- Phased rollout with security testing at each stage
- Continuous monitoring and alerting
- Regular security assessments and penetration testing

## Success Metrics & KPIs

### Security Metrics
- Zero critical vulnerabilities in production
- 100% STQC requirement compliance
- <1 second security scan execution time
- 99.9% uptime with security monitoring

### Compliance Metrics
- All 29 STQC requirements implemented
- Documented evidence for each requirement
- Regular audit trail maintenance
- Incident response time <15 minutes

## Budget Estimation

### Infrastructure & Tools (₹15-20 Lakhs)
- Azure security services
- Security testing tools licenses
- Monitoring and SIEM solutions
- SSL certificates and security hardware

### Professional Services (₹8-12 Lakhs)
- Security consultants
- Penetration testing services
- Compliance audit support
- Training and certification

### Internal Resources (₹2-3 Lakhs)
- Development team training
- Security team setup
- Documentation and procedures

## Timeline & Milestones

### Week 1-4: Foundation
- Infrastructure security setup
- Basic hardening implementation
- Security testing pipeline setup

### Week 5-8: Application Security
- Vulnerability remediation
- Authentication system overhaul
- Encryption implementation

### Week 9-12: Testing & Validation
- Comprehensive security testing
- Penetration testing
- Compliance validation

### Week 13-16: Documentation & Audit Prep
- Complete documentation
- Final security assessment
- STQC audit preparation

## Next Steps
1. Approve PRD and budget allocation
2. Setup project team and governance
3. Begin Phase 1 implementation
4. Establish security testing pipeline
5. Start vulnerability remediation

---
**Document Version**: 1.0
**Last Updated**: January 2025
**Owner**: Security & Compliance Team
**Approvers**: CTO, CISO, Project Manager