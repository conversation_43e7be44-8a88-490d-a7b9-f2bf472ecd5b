# DETAILED APPROACH ANALYSIS - ARANCARE STQC COMPLIANCE
# Folder-by-Folder Security Implementation Strategy

## OVERVIEW
This document provides a comprehensive approach to implementing STQC compliance by analyzing each folder in the AranCare codebase and defining specific security fixes and implementations needed.

## APPS FOLDER ANALYSIS

### 1. APPS/FRONTEND FOLDER APPROACH
**Location**: `/apps/frontend/`
**Priority**: CRITICAL - Contains main application logic

#### 1.1 SRC/APP FOLDER SECURITY APPROACH
**Location**: `/apps/frontend/src/app/`

**API Routes Security (`/app/api/`)**
- **Current State**: 50+ API endpoints with varying security levels
- **Issues Found**:
  - Inconsistent authentication
  - Missing input validation
  - Potential PHI data exposure
  - No rate limiting
  - Inconsistent error handling

**Security Implementation Approach**:
```typescript
// Step 1: Create API Security Middleware
// File: src/middleware/api-security.ts
export function apiSecurityMiddleware(handler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) {
  return async (req: NextRequest, res: NextResponse) => {
    // 1. Authentication validation
    // 2. Input sanitization
    // 3. Rate limiting
    // 4. Audit logging
    // 5. Error handling
    return handler(req, res);
  };
}

// Step 2: Apply to all API routes
// Example: app/api/patients/route.ts
export const POST = apiSecurityMiddleware(async (req, res) => {
  // Secure implementation
});
```

**Authentication Routes (`/app/api/auth/`)**
- **Current State**: NextAuth implementation with hardcoded credentials
- **Critical Issues**:
  - Hardcoded demo/admin credentials in route.ts
  - Mock authentication in auth-context.tsx
  - Fallback credentials in login/route.ts

**Security Fix Approach**:
```typescript
// Step 1: Remove hardcoded credentials
// File: app/api/auth/[...nextauth]/route.ts
const authOptions: AuthOptions = {
  providers: [
    CredentialsProvider({
      async authorize(credentials) {
        // REMOVE: Hardcoded credential checks
        // KEEP: Only database authentication
        const user = await db.user.findUnique({
          where: { email: credentials.email }
        });
        
        if (!user || !user.password) return null;
        
        const isValid = await comparePassword(
          credentials.password, 
          user.password
        );
        
        return isValid ? user : null;
      }
    })
  ]
};
```

#### 1.2 SRC/COMPONENTS FOLDER SECURITY APPROACH
**Location**: `/apps/frontend/src/components/`

**Patient Components Security**
- **Files**: `patient-registration-form.tsx`, `patient-search-banner.tsx`
- **Issues**: Direct fetch calls, no input validation, PHI exposure risk
- **Approach**:
```typescript
// Step 1: Create secure API service
// File: services/api/patients-api.ts
export class PatientsAPI {
  private async secureRequest(url: string, options: RequestInit) {
    // Add authentication headers
    // Add request validation
    // Add audit logging
    return fetch(url, options);
  }
  
  async createPatient(data: CreatePatientInput) {
    const validatedData = CreatePatientSchema.parse(data);
    return this.secureRequest('/api/patients', {
      method: 'POST',
      body: JSON.stringify(validatedData)
    });
  }
}

// Step 2: Update components to use secure API
// File: components/patient-registration-form.tsx
const PatientRegistrationForm = () => {
  const handleSubmit = async (data) => {
    try {
      await patientsAPI.createPatient(data);
    } catch (error) {
      // Secure error handling - no PHI exposure
      setError('Registration failed. Please try again.');
    }
  };
};
```

**Consultation Components Security**
- **Files**: Consultation workflow components
- **Issues**: Complex medical data handling, access control gaps
- **Approach**:
```typescript
// Step 1: Implement role-based access
// File: hooks/use-consultation-access.ts
export const useConsultationAccess = (consultationId: string) => {
  const { user } = useAuth();
  
  return useMemo(() => ({
    canView: hasPermission(user, 'consultation:view', consultationId),
    canEdit: hasPermission(user, 'consultation:edit', consultationId),
    canDelete: hasPermission(user, 'consultation:delete', consultationId)
  }), [user, consultationId]);
};

// Step 2: Secure medical data display
// File: components/consultations/consultation-details.tsx
const ConsultationDetails = ({ consultationId }) => {
  const access = useConsultationAccess(consultationId);
  
  if (!access.canView) {
    return <AccessDenied />;
  }
  
  // Secure data rendering with audit logging
};
```

#### 1.3 SRC/SERVICES FOLDER SECURITY APPROACH
**Location**: `/apps/frontend/src/services/`

**ABDM Services Security**
- **Files**: `services/abdm/` folder
- **Issues**: Government API integration security
- **Approach**:
```typescript
// Step 1: Secure ABDM authentication
// File: services/abdm/auth-service.ts
export class ABDMAuthService {
  private static tokenCache: TokenCache | null = null;
  
  static async getSecureToken(): Promise<string> {
    // Use server-only environment variables
    // Implement token caching with expiry
    // Add comprehensive error handling
    // Log authentication attempts
  }
  
  static async validateABHAData(data: any): Promise<boolean> {
    // Validate ABHA data without exposing PHI
    // Implement data sanitization
    // Add audit logging
  }
}

// Step 2: Secure health record handling
// File: services/abdm/health-record-service.ts
export class HealthRecordService {
  async uploadHealthRecord(record: HealthRecord) {
    // Validate record data
    // Encrypt sensitive information
    // Add consent validation
    // Implement audit logging
    // Handle errors securely
  }
}
```

**Patient Service Security**
- **File**: `services/patient-service.ts`
- **Issues**: Direct database access, PHI handling
- **Approach**:
```typescript
// Step 1: Implement secure patient service
export class SecurePatientService {
  async searchPatients(query: SearchQuery): Promise<PatientSearchResult[]> {
    // Validate search parameters
    const validatedQuery = SearchQuerySchema.parse(query);
    
    // Add rate limiting
    await this.checkRateLimit(validatedQuery.userId);
    
    // Perform secure search
    const results = await this.performSearch(validatedQuery);
    
    // Filter results based on user permissions
    const filteredResults = await this.filterByPermissions(results, validatedQuery.userId);
    
    // Log search activity
    await this.logSearchActivity(validatedQuery, filteredResults.length);
    
    // Return sanitized results (no PHI in logs)
    return this.sanitizeResults(filteredResults);
  }
}
```

#### 1.4 SRC/LIB FOLDER SECURITY APPROACH
**Location**: `/apps/frontend/src/lib/`

**Authentication Libraries**
- **Files**: `auth.ts`, `auth-cookies.ts`, `session.ts`
- **Issues**: Session management, cookie security
- **Approach**:
```typescript
// Step 1: Secure session management
// File: lib/secure-session.ts
export class SecureSessionManager {
  static async createSession(user: User): Promise<SessionToken> {
    // Generate cryptographically secure session token
    const token = crypto.randomBytes(32).toString('hex');
    
    // Set secure session with expiry
    const session = {
      token,
      userId: user.id,
      expiresAt: new Date(Date.now() + SESSION_DURATION),
      createdAt: new Date(),
      ipAddress: this.getClientIP(),
      userAgent: this.getUserAgent()
    };
    
    // Store in secure storage with encryption
    await this.storeSecureSession(session);
    
    // Log session creation
    await this.logSessionActivity('CREATE', session);
    
    return token;
  }
  
  static async validateSession(token: string): Promise<User | null> {
    // Validate token format
    if (!this.isValidTokenFormat(token)) return null;
    
    // Retrieve session securely
    const session = await this.getSecureSession(token);
    if (!session || this.isExpired(session)) {
      await this.logSessionActivity('INVALID', { token });
      return null;
    }
    
    // Update last activity
    await this.updateLastActivity(session);
    
    // Return user data
    return this.getUserFromSession(session);
  }
}

// Step 2: Secure cookie handling
// File: lib/secure-cookies.ts
export class SecureCookieManager {
  static setSecureCookie(name: string, value: string, options: CookieOptions = {}) {
    const secureOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      path: '/',
      maxAge: options.maxAge || 86400, // 24 hours default
      ...options
    };
    
    // Encrypt cookie value
    const encryptedValue = this.encryptCookieValue(value);
    
    // Set cookie with secure options
    cookies().set(name, encryptedValue, secureOptions);
    
    // Log cookie activity
    this.logCookieActivity('SET', name);
  }
  
  static getSecureCookie(name: string): string | null {
    const cookie = cookies().get(name);
    if (!cookie) return null;
    
    try {
      // Decrypt cookie value
      const decryptedValue = this.decryptCookieValue(cookie.value);
      
      // Log cookie access
      this.logCookieActivity('GET', name);
      
      return decryptedValue;
    } catch (error) {
      // Log tampering attempt
      this.logCookieActivity('TAMPER', name, error);
      return null;
    }
  }
}
```

**Database Security**
- **Files**: `db.ts`, `prisma.ts`
- **Issues**: Database connection security, query safety
- **Approach**:
```typescript
// Step 1: Secure database wrapper
// File: lib/secure-db.ts
export class SecureDatabase {
  private static instance: PrismaClient;
  
  static getInstance(): PrismaClient {
    if (!this.instance) {
      this.instance = new PrismaClient({
        log: ['query', 'error', 'warn'],
        datasources: {
          db: {
            url: this.getSecureConnectionString()
          }
        }
      });
      
      // Add query logging middleware
      this.instance.$use(this.queryLoggingMiddleware);
      
      // Add access control middleware
      this.instance.$use(this.accessControlMiddleware);
    }
    
    return this.instance;
  }
  
  private static queryLoggingMiddleware: Prisma.Middleware = async (params, next) => {
    const start = Date.now();
    const result = await next(params);
    const duration = Date.now() - start;
    
    // Log database queries for audit
    await this.logDatabaseQuery({
      model: params.model,
      action: params.action,
      duration,
      timestamp: new Date()
    });
    
    return result;
  };
  
  private static accessControlMiddleware: Prisma.Middleware = async (params, next) => {
    // Implement row-level security
    // Add user context to queries
    // Validate access permissions
    
    const userContext = this.getCurrentUserContext();
    if (!userContext) {
      throw new Error('Unauthorized database access');
    }
    
    // Add user filters to queries
    const modifiedParams = this.addUserFilters(params, userContext);
    
    return next(modifiedParams);
  };
}
```

#### 1.5 SRC/MIDDLEWARE FOLDER SECURITY APPROACH
**Location**: `/apps/frontend/src/middleware/`

**Security Headers Enhancement**
- **File**: `security-headers.ts`
- **Current State**: Basic security headers implemented
- **Enhancement Approach**:
```typescript
// Enhanced security headers for STQC compliance
export function enhancedSecurityHeaders(request: NextRequest, response: NextResponse): NextResponse {
  // Content Security Policy - Healthcare specific
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "connect-src 'self' https://api.abdm.gov.in https://abhasbx.abdm.gov.in https://*.azure.com",
    "frame-src 'none'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests"
  ].join("; ");

  // Set comprehensive security headers
  const securityHeaders = {
    'Content-Security-Policy': csp,
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'X-XSS-Protection': '1; mode=block',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    'X-Healthcare-App': 'AranCare',
    'Cache-Control': 'no-store, no-cache, must-revalidate, private',
    'Pragma': 'no-cache',
    'Expires': '0'
  };

  // Add HSTS in production
  if (process.env.NODE_ENV === 'production') {
    securityHeaders['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload';
  }

  // Apply all headers
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  // Log security header application
  this.logSecurityHeaders(request.url, securityHeaders);

  return response;
}
```

**Authentication Middleware Enhancement**
- **File**: `middleware.ts`
- **Current State**: Basic route protection
- **Enhancement Approach**:
```typescript
// Enhanced authentication middleware
export async function enhancedAuthMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Define route access rules
  const routeRules = {
    public: ['/sign-in', '/sign-up', '/verify', '/'],
    authenticated: ['/dashboard', '/patients', '/consultations'],
    roles: {
      '/admin': ['admin', 'super-admin'],
      '/doctor': ['doctor', 'admin'],
      '/nurse': ['nurse', 'doctor', 'admin']
    }
  };
  
  // Check if route requires authentication
  const requiresAuth = !routeRules.public.some(route => pathname.startsWith(route));
  
  if (requiresAuth) {
    // Validate session
    const session = await validateSecureSession(request);
    
    if (!session) {
      // Log unauthorized access attempt
      await logSecurityEvent('UNAUTHORIZED_ACCESS', {
        path: pathname,
        ip: request.ip,
        userAgent: request.headers.get('user-agent')
      });
      
      return NextResponse.redirect(new URL('/sign-in', request.url));
    }
    
    // Check role-based access
    const requiredRoles = routeRules.roles[pathname];
    if (requiredRoles && !requiredRoles.includes(session.user.role)) {
      // Log access denied
      await logSecurityEvent('ACCESS_DENIED', {
        userId: session.user.id,
        path: pathname,
        role: session.user.role,
        requiredRoles
      });
      
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
    
    // Log successful access
    await logSecurityEvent('ACCESS_GRANTED', {
      userId: session.user.id,
      path: pathname,
      role: session.user.role
    });
  }
  
  // Apply security headers to response
  const response = NextResponse.next();
  return enhancedSecurityHeaders(request, response);
}
```

### 2. APPS/NODE-SERVER FOLDER APPROACH
**Location**: `/apps/node-server/`
**Priority**: HIGH - Contains encryption services

#### 2.1 FIDELIUS API SECURITY APPROACH
**Location**: `/apps/node-server/src/fidelius-api/`

**Current State Analysis**:
- API key authentication middleware exists
- Encryption/decryption controllers implemented
- Route protection in place

**Security Enhancement Approach**:
```javascript
// Step 1: Enhanced authentication middleware
// File: src/middleware/enhanced-auth.js
const enhancedApiKeyAuth = (req, res, next) => {
  const apiKey = req.headers['x-api-key'];
  const validApiKey = process.env.API_KEY;
  
  // Enhanced validation
  if (!validApiKey) {
    logger.error('API_KEY environment variable not configured');
    return res.status(500).json({
      success: false,
      error: 'Server configuration error',
      timestamp: new Date().toISOString()
    });
  }
  
  if (!apiKey) {
    // Log missing API key attempt
    logger.warn('API key missing', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
      timestamp: new Date().toISOString()
    });
    
    return res.status(401).json({
      success: false,
      error: 'API key is required',
      timestamp: new Date().toISOString()
    });
  }
  
  // Constant-time comparison to prevent timing attacks
  const isValidKey = crypto.timingSafeEqual(
    Buffer.from(apiKey),
    Buffer.from(validApiKey)
  );
  
  if (!isValidKey) {
    // Log invalid API key attempt
    logger.warn('Invalid API key provided', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
      keyPrefix: apiKey.substring(0, 4) + '****',
      timestamp: new Date().toISOString()
    });
    
    return res.status(401).json({
      success: false,
      error: 'Invalid API key',
      timestamp: new Date().toISOString()
    });
  }
  
  // Log successful authentication
  logger.info('API key authenticated successfully', {
    ip: req.ip,
    endpoint: req.path,
    timestamp: new Date().toISOString()
  });
  
  next();
};

// Step 2: Enhanced controllers with audit logging
// File: src/fidelius-api/enhanced-controllers.js
const enhancedEncrypt = async (req, res) => {
  const startTime = Date.now();
  
  try {
    const {
      string_to_encrypt,
      sender_nonce,
      requester_nonce,
      sender_private_key,
      requester_public_key
    } = req.body;
    
    // Input validation
    if (!string_to_encrypt || !sender_nonce || !requester_nonce) {
      logger.warn('Invalid encryption request - missing required fields', {
        ip: req.ip,
        missingFields: {
          string_to_encrypt: !string_to_encrypt,
          sender_nonce: !sender_nonce,
          requester_nonce: !requester_nonce
        }
      });
      
      return res.status(400).json({
        success: false,
        error: 'Missing required encryption parameters'
      });
    }
    
    // Perform encryption
    const result = await performSecureEncryption({
      string_to_encrypt,
      sender_nonce,
      requester_nonce,
      sender_private_key,
      requester_public_key
    });
    
    const duration = Date.now() - startTime;
    
    // Log successful encryption
    logger.info('Encryption completed successfully', {
      ip: req.ip,
      duration,
      dataSize: string_to_encrypt.length,
      timestamp: new Date().toISOString()
    });
    
    return res.json({
      success: true,
      data: result.encryptedData,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    const duration = Date.now() - startTime;
    
    // Log encryption error
    logger.error('Encryption failed', {
      ip: req.ip,
      duration,
      error: error.message,
      timestamp: new Date().toISOString()
    });
    
    return res.status(500).json({
      success: false,
      error: 'Encryption operation failed',
      timestamp: new Date().toISOString()
    });
  }
};
```

#### 2.2 SERVER CONFIGURATION SECURITY
**Location**: `/apps/node-server/src/index.js`

**CORS Security Enhancement**:
```javascript
// Enhanced CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    // Define allowed origins
    const allowedOrigins = [
      'http://localhost:3005',
      'https://arancare.flinkk.io',
      'https://app.arancare.com'
    ];
    
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      // Log unauthorized origin attempt
      logger.warn('CORS blocked unauthorized origin', {
        origin,
        timestamp: new Date().toISOString()
      });
      
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-api-key'],
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));
```

## PACKAGES FOLDER ANALYSIS

### 3. SHARED PACKAGES SECURITY APPROACH
**Location**: `/packages/`

**Shared Database Security**
- **Location**: `/packages/shared-database/`
- **Approach**: Implement centralized database security policies

**UI Components Security**
- **Location**: `/packages/ui/`
- **Approach**: Secure component props validation and XSS prevention

**Services Security**
- **Location**: `/packages/services/`
- **Approach**: Standardized API service security patterns

## IMPLEMENTATION TIMELINE

### PHASE 1: CRITICAL SECURITY FIXES (Week 1)
1. Remove hardcoded credentials
2. Enable API key authentication
3. Fix CORS configuration
4. Secure environment variables
5. Implement input validation framework

### PHASE 2: MODULE SECURITY (Week 2)
6. Secure authentication system
7. Implement patient module security
8. Secure consultation workflows
9. Enhance ABDM integration security
10. Secure file upload mechanisms

### PHASE 3: INFRASTRUCTURE SECURITY (Week 3)
11. Database security implementation
12. Session management security
13. WebSocket security
14. Audit logging implementation
15. Security monitoring setup

### PHASE 4: TESTING & COMPLIANCE (Week 4)
16. Comprehensive security testing
17. STQC compliance validation
18. Performance testing
19. Documentation completion
20. Audit preparation

This approach ensures systematic security implementation across all components of the AranCare application.
